
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./components/ThemeProvider";
import { KnowledgeProvider } from "./contexts/KnowledgeContext";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import Layout from "./components/layout/Layout";
import Dashboard from "./pages/Dashboard";
import Curate from "./pages/Curate";
import Users from "./pages/Users";

import EmployeeTrainingAssignment from "./pages/EmployeeTrainingAssignment";
import CuratorAssignment from "./pages/CuratorAssignment";
import Broadcast from "./pages/Broadcast";
import Certificate from "./pages/Certificate";
import Insights from "./pages/Insights";
import Classroom from "./pages/Classroom";
import Reports from "./pages/Reports";
import Settings from "./pages/Settings";
import H5PContent from "./pages/H5PContent";

import TrainingLibraryAssignment from "./pages/TrainingLibraryAssignment";
import DocLibraryAssignment from "./pages/DocLibraryAssignment";
import FormsLibraryAssignment from "./pages/FormsLibraryAssignment";
import ChecklistLibraryAssignment from "./pages/ChecklistLibraryAssignment";


const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider>
      <KnowledgeProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <Routes>
              <Route path="/" element={<Layout />}>
                <Route index element={<Index />} />
                <Route path="dashboard" element={<Dashboard />} />
                <Route path="curate" element={<Curate />} />
                <Route path="users" element={<Users />} />
                <Route path="employee-training-assignment" element={<EmployeeTrainingAssignment />} />
                <Route path="training-library-assignment" element={<TrainingLibraryAssignment />} />
                <Route path="doc-library-assignment" element={<DocLibraryAssignment />} />
                <Route path="forms-library-assignment" element={<FormsLibraryAssignment />} />
                <Route path="checklist-library-assignment" element={<ChecklistLibraryAssignment />} />

                <Route path="curator-assignment" element={<CuratorAssignment />} />
                <Route path="broadcast" element={<Broadcast />} />
                <Route path="certificate" element={<Certificate />} />
                <Route path="insights" element={<Insights />} />
                <Route path="classroom" element={<Classroom />} />
                <Route path="reports" element={<Reports />} />
                <Route path="h5p-content" element={<H5PContent />} />
                <Route path="settings" element={<Settings />} />
                <Route path="*" element={<NotFound />} />
              </Route>
            </Routes>
          </BrowserRouter>
        </TooltipProvider>
      </KnowledgeProvider>
    </ThemeProvider>
  </QueryClientProvider>
);

export default App;
