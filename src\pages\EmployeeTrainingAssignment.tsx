import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { MultiSelectCheckbox } from "@/components/MultiSelectCheckbox";
import { ScrollArea } from "@/components/ui/scroll-area";
import AssignedContentDropdown from "@/components/AssignedContentDropdown";
import SelectedItemTag from "@/components/SelectedItemTag";
import {
  Search,
  Plus,
  Download,
  Filter,
  Eye,
  X,
  Users,
  FileDown,
  Check,
  ChevronDown,
  Trash2,
} from "lucide-react";
import { Card } from "@/components/ui/card";
import { toast } from "@/hooks/use-toast";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { motion } from "framer-motion";
import SelectedUnitsWithHierarchy from "@/components/SelectedUnitsWithHierarchy";
import { TableColumnFilter } from "@/components/TableColumnFilter";
import { UserSelectionDialog } from "@/components/UserSelectionDialog";
import { ViewAssignmentDialog } from "@/components/ViewAssignmentDialog";

import {
  generateTrainingAssignmentPDF,
  exportTrainingAssignmentsToExcel,
} from "@/lib/pdf-utils";
import * as XLSX from "xlsx";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Tabs,
  TabsList,
  TabsTrigger,
  TabsContent,
} from "@/components/ui/tabs";

// Define the TrainingAssignment type
interface TrainingAssignment {
  id: string;
  employeeIds: string[]; // Array of employee IDs
  employeeName?: string; // For backward compatibility
  trainingProgram: string;
  businessUnit: string;
  departmentGroup: string;
  department: string;
  division: string;
  subDivision: string;
  category: string;
  grade: string;
  designation: string;
  status: string;
  assignmentStatus: "Active" | "Suspended"; // New field for assignment status
  assignedDate: string;
  dueDate?: string;
  notes?: string;
}

// Mock data for filters
const businessUnits = ["KCN", "KTN", "KHC", "KVP", "KRR", "KHO", "KHS"];

// Area → Topic → Unit hierarchy for Training
const trainingAreas = ["Clinical", "Administrative", "Technical"];

const trainingTopics = {
  Clinical: ["Patient Care", "Emergency Medicine", "Pharmacy", "Nursing"],
  Administrative: ["HR Management", "Finance", "Operations", "Quality Control"],
  Technical: [
    "IT Systems",
    "Medical Equipment",
    "Facilities",
    "Data Management",
  ],
};

const trainingUnits = {
  "Patient Care": [
    "Basic Patient Care",
    "Advanced Patient Care",
    "Critical Care",
  ],
  "Emergency Medicine": ["Triage", "Emergency Response", "Trauma Management"],
  Pharmacy: [
    "Medication Management",
    "Pharmacy Operations",
    "Drug Interactions",
  ],
  Nursing: ["Basic Nursing", "Advanced Nursing", "Specialized Nursing"],
  "HR Management": [
    "Recruitment",
    "Employee Relations",
    "Performance Management",
  ],
  Finance: ["Budgeting", "Financial Reporting", "Cost Control"],
  Operations: [
    "Workflow Optimization",
    "Resource Management",
    "Process Improvement",
  ],
  "Quality Control": ["Quality Assurance", "Compliance", "Risk Management"],
  "IT Systems": ["EHR Systems", "Network Management", "Cybersecurity"],
  "Medical Equipment": [
    "Equipment Operation",
    "Maintenance",
    "Troubleshooting",
  ],
  Facilities: [
    "Facility Management",
    "Safety Protocols",
    "Environmental Services",
  ],
  "Data Management": ["Data Entry", "Data Analysis", "Reporting"],
};

// Checklist → Form → Document hierarchy for CFD
const cfdAreas = [
  "Patient Safety Checklist",
  "Medication Checklist",
  "Equipment Checklist",
];

const cfdTopics = {
  "Patient Safety Checklist": [
    "Admission Form",
    "Discharge Form",
    "Daily Assessment Form",
  ],
  "Medication Checklist": [
    "Prescription Form",
    "Administration Form",
    "Inventory Form",
  ],
  "Equipment Checklist": [
    "Maintenance Form",
    "Calibration Form",
    "Inspection Form",
  ],
};

const cfdUnits = {
  "Admission Form": [
    "Patient Identification Document",
    "Medical History Document",
    "Initial Assessment Document",
  ],
  "Discharge Form": [
    "Discharge Summary Document",
    "Follow-up Instructions Document",
    "Medication List Document",
  ],
  "Daily Assessment Form": [
    "Vital Signs Document",
    "Pain Assessment Document",
    "Progress Notes Document",
  ],
  "Prescription Form": [
    "Medication Order Document",
    "Dosage Calculation Document",
    "Contraindications Document",
  ],
  "Administration Form": [
    "Administration Schedule Document",
    "Patient Response Document",
    "Side Effects Document",
  ],
  "Inventory Form": [
    "Stock Level Document",
    "Expiry Tracking Document",
    "Reorder Document",
  ],
  "Maintenance Form": [
    "Maintenance Schedule Document",
    "Service History Document",
    "Parts Replacement Document",
  ],
  "Calibration Form": [
    "Calibration Procedure Document",
    "Accuracy Verification Document",
    "Calibration Log Document",
  ],
  "Inspection Form": [
    "Safety Inspection Document",
    "Functionality Test Document",
    "Compliance Document",
  ],
};

// State for dynamic hierarchies - will be set in useEffect based on assignment type

// Organizational hierarchy
const departmentGroups = [
  "Clinical Services",
  "Emergency Services",
  "Management",
  "Quality Assurance",
  "Information Management",
  "Infection Prevention",
  "IT Services",
  "Patient Services",
  "Ethics Committee",
  "Pharmacy Services",
];

// Department groups to departments mapping
const departmentsByGroup = {
  "Clinical Services": ["Nursing", "Intensive Care", "Outpatient Services"],
  "Emergency Services": [
    "Emergency Medicine",
    "Trauma Care",
    "Ambulatory Services",
  ],
  Management: [
    "Hospital Administration",
    "Operations Management",
    "Strategic Planning",
  ],
  "Quality Assurance": ["Quality Control", "Risk Management", "Compliance"],
  "Information Management": [
    "Health Information",
    "Medical Records",
    "Data Analytics",
  ],
  "Infection Prevention": [
    "Epidemiology",
    "Infection Control",
    "Sterilization Services",
  ],
  "IT Services": [
    "Healthcare IT",
    "Systems Administration",
    "Technical Support",
  ],
  "Patient Services": [
    "Patient Relations",
    "Patient Advocacy",
    "Patient Experience",
  ],
  "Ethics Committee": ["Medical Ethics", "Clinical Ethics", "Research Ethics"],
  "Pharmacy Services": [
    "Pharmacy",
    "Medication Management",
    "Pharmaceutical Research",
  ],
};

// Departments to divisions mapping
const divisionsByDepartment = {
  Nursing: ["Patient Care", "Specialized Nursing", "Nursing Education"],
  "Emergency Medicine": ["Trauma Care", "Emergency Response", "Critical Care"],
  "Hospital Administration": [
    "Operations",
    "Strategic Planning",
    "Resource Management",
  ],
  "Quality Control": [
    "Patient Safety",
    "Quality Improvement",
    "Standards Compliance",
  ],
  "Health Information": ["Records", "Data Management", "Information Systems"],
  Epidemiology: ["Control Measures", "Outbreak Investigation", "Surveillance"],
  "Healthcare IT": ["Clinical Systems", "Infrastructure", "Applications"],
  "Patient Relations": [
    "Communication",
    "Patient Advocacy",
    "Service Excellence",
  ],
  "Medical Ethics": ["Clinical Ethics", "Research Ethics", "Ethics Education"],
  Pharmacy: [
    "Medication Management",
    "Clinical Pharmacy",
    "Pharmacy Operations",
  ],
};

// Divisions to sub-divisions mapping
const subDivisionsByDivision = {
  "Patient Care": ["Intensive Care", "General Care", "Specialized Care"],
  "Trauma Care": [
    "First Response",
    "Trauma Assessment",
    "Critical Intervention",
  ],
  Operations: [
    "Department Management",
    "Resource Allocation",
    "Process Improvement",
  ],
  "Patient Safety": [
    "Standards Compliance",
    "Safety Protocols",
    "Incident Management",
  ],
  Records: ["Electronic Records", "Records Management", "Documentation"],
  "Control Measures": [
    "Outbreak Prevention",
    "Infection Control",
    "Isolation Protocols",
  ],
  "Clinical Systems": [
    "EHR Implementation",
    "Clinical Applications",
    "System Integration",
  ],
  Communication: [
    "Patient Interaction",
    "Family Communication",
    "Interdepartmental Communication",
  ],
  "Clinical Ethics": [
    "Ethical Decision Making",
    "Ethics Consultation",
    "Ethics Policy",
  ],
  "Medication Management": [
    "Dispensing",
    "Medication Safety",
    "Medication Reconciliation",
  ],
};

// Sub-divisions to categories mapping
const categoriesBySubDivision = {
  "Intensive Care": ["Clinical", "Critical Care", "Specialized"],
  "First Response": ["Clinical", "Emergency", "Trauma"],
  "Department Management": ["Non-Clinical", "Administrative", "Management"],
  "Standards Compliance": ["Non-Clinical", "Quality", "Regulatory"],
  "Electronic Records": ["Technical", "Information Systems", "Data Management"],
  "Outbreak Prevention": ["Clinical", "Preventive", "Infection Control"],
  "EHR Implementation": ["Technical", "Systems", "Implementation"],
  "Patient Interaction": [
    "Non-Clinical",
    "Communication",
    "Patient Experience",
  ],
  "Ethical Decision Making": ["Clinical", "Ethics", "Decision Support"],
  Dispensing: ["Clinical", "Pharmacy", "Medication"],
};

// Categories to grades mapping
const gradesByCategory = {
  Clinical: ["Junior", "Mid-level", "Senior", "Lead"],
  "Non-Clinical": ["Entry-level", "Associate", "Senior", "Manager"],
  Technical: ["Junior", "Mid-level", "Senior", "Specialist"],
};

// Grades to designations mapping
const designationsByGrade = {
  Junior: ["Nurse Assistant", "Junior Technician", "Resident"],
  "Mid-level": ["Registered Nurse", "Technician", "Physician"],
  Senior: ["Head Nurse", "Senior Technician", "Attending Physician"],
  Lead: ["Nursing Director", "Technical Lead", "Chief Physician"],
  "Entry-level": ["Administrative Assistant", "Records Clerk", "Support Staff"],
  Associate: [
    "Administrative Coordinator",
    "Records Specialist",
    "Department Coordinator",
  ],
  Manager: [
    "Department Manager",
    "Administrative Manager",
    "Operations Manager",
  ],
  Specialist: ["IT Specialist", "Systems Specialist", "Technical Specialist"],
};

const statuses = ["Not Started", "In Progress", "Completed"];
const assignmentStatuses = ["Active", "Suspended"];

// Additional data structures for library content
const documentAreas = ["Patient Safety Checklist", "Medication Checklist", "Equipment Checklist"];
const documentTopics = {
  "Patient Safety Checklist": ["Admission Form", "Discharge Form", "Daily Assessment Form"],
  "Medication Checklist": ["Prescription Form", "Administration Form", "Inventory Form"],
  "Equipment Checklist": ["Maintenance Form", "Calibration Form", "Inspection Form"],
};

const documentUnits = {
  "Patient Identification Document": ["ID Verification", "Wristband Check", "Photo Verification"],
  "Medical History Document": ["Past Medical History", "Family History", "Social History"],
  "Initial Assessment Document": ["Physical Assessment", "Mental Status", "Risk Assessment"],
  "Discharge Summary Document": ["Diagnosis Summary", "Treatment Summary", "Follow-up Plan"],
  "Follow-up Instructions Document": ["Medication Instructions", "Activity Restrictions", "Appointment Schedule"],
  "Medication List Document": ["Current Medications", "Allergies", "Discontinued Medications"],
  "Vital Signs Document": ["Temperature", "Blood Pressure", "Heart Rate", "Respiratory Rate"],
  "Pain Assessment Document": ["Pain Scale", "Pain Location", "Pain Management"],
  "Progress Notes Document": ["Daily Progress", "Treatment Response", "Care Plan Updates"],
  "Medication Order Document": ["Prescription Details", "Dosage", "Frequency"],
  "Dosage Calculation Document": ["Weight-based Dosing", "Age Adjustments", "Renal Adjustments"],
  "Contraindications Document": ["Drug Allergies", "Medical Conditions", "Drug Interactions"],
  "Administration Schedule Document": ["Timing", "Route", "Special Instructions"],
  "Patient Response Document": ["Therapeutic Response", "Adverse Reactions", "Monitoring"],
  "Side Effects Document": ["Common Side Effects", "Serious Reactions", "Management"],
  "Stock Level Document": ["Current Inventory", "Minimum Levels", "Reorder Points"],
  "Expiry Tracking Document": ["Expiry Dates", "Rotation Schedule", "Disposal"],
  "Reorder Document": ["Purchase Orders", "Vendor Information", "Delivery Schedule"],
  "Maintenance Schedule Document": ["Preventive Maintenance", "Scheduled Services", "Warranty"],
  "Service History Document": ["Past Services", "Repairs", "Upgrades"],
  "Parts Replacement Document": ["Replaced Parts", "Part Numbers", "Installation"],
  "Calibration Procedure Document": ["Calibration Steps", "Standards", "Equipment"],
  "Accuracy Verification Document": ["Test Results", "Tolerance Checks", "Adjustments"],
  "Calibration Log Document": ["Calibration Records", "Results", "Technician Notes"],
  "Safety Inspection Document": ["Safety Checks", "Hazard Assessment", "Compliance Verification"],
  "Functionality Test Document": ["Performance Tests", "Operational Checks", "Quality Assurance"],
  "Compliance Document": ["Regulatory Requirements", "Standards Compliance", "Audit Trail"],
};

const formAreas = ["Patient Safety Checklist", "Medication Checklist", "Equipment Checklist"];
const formTopics = {
  "Patient Safety Checklist": ["Admission Form", "Discharge Form", "Daily Assessment Form"],
  "Medication Checklist": ["Prescription Form", "Administration Form", "Inventory Form"],
  "Equipment Checklist": ["Maintenance Form", "Calibration Form", "Inspection Form"],
};

const formUnits = {
  "Admission Form": ["Patient Demographics", "Medical History", "Insurance Information", "Emergency Contacts"],
  "Discharge Form": ["Discharge Instructions", "Medication List", "Follow-up Appointments", "Care Plan"],
  "Daily Assessment Form": ["Vital Signs", "Pain Assessment", "Mobility Status", "Mental Status"],
  "Prescription Form": ["Medication Details", "Dosage Instructions", "Prescriber Information", "Patient Information"],
  "Administration Form": ["Administration Time", "Dosage Given", "Route", "Patient Response"],
  "Inventory Form": ["Stock Levels", "Expiry Dates", "Lot Numbers", "Storage Conditions"],
  "Maintenance Form": ["Maintenance Tasks", "Parts Replaced", "Service Date", "Technician Notes"],
  "Calibration Form": ["Calibration Standards", "Test Results", "Adjustments Made", "Next Due Date"],
  "Inspection Form": ["Safety Checks", "Performance Tests", "Compliance Status", "Recommendations"],
};

const checklistAreas = ["Patient Safety Checklist", "Medication Checklist", "Equipment Checklist"];
const checklistTopics = {
  "Patient Safety Checklist": ["Patient Safety Checklist"],
  "Medication Checklist": ["Medication Checklist"],
  "Equipment Checklist": ["Equipment Checklist"],
};

const checklistUnits = {
  "Patient Safety Checklist": [
    "Patient Identification Verification",
    "Allergy Check",
    "Fall Risk Assessment",
    "Infection Control Measures",
    "Emergency Equipment Check",
    "Vital Signs Monitoring",
    "Pain Assessment",
    "Discharge Planning"
  ],
  "Medication Checklist": [
    "Medication Reconciliation",
    "Dosage Verification",
    "Administration Route Check",
    "Drug Interaction Review",
    "Patient Education",
    "Side Effect Monitoring",
    "Storage Requirements",
    "Expiry Date Verification"
  ],
  "Equipment Checklist": [
    "Equipment Functionality Test",
    "Safety Inspection",
    "Calibration Verification",
    "Maintenance Schedule Check",
    "User Training Verification",
    "Documentation Review",
    "Compliance Audit",
    "Emergency Procedures"
  ],
};

// Generate table data functions
const generateTrainingTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    isMainTopic: boolean;
  }> = [];

  Object.entries(trainingTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      // Add only main topic row without units
      data.push({
        area,
        topic,
        topicId: `T-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`,
        version: "v1.0",
        responsibleDept: area === "Clinical" ? "Clinical Services" :
                        area === "Administrative" ? "Management" : "IT Services",
        contentOwner: `${topic} Team`,
        isMainTopic: true,
      });
    });
  });

  return data;
};

const generateDocumentTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit: string;
  }> = [];

  Object.entries(documentTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = documentUnits[topic] || [];
      const baseTopicId = `DOC-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`;
      const responsibleDept = area.includes("Patient") ? "Clinical Services" :
                             area.includes("Medication") ? "Pharmacy Services" : "Technical Services";
      const contentOwner = area.includes("Patient") ? "Clinical Documentation Team" :
                          area.includes("Medication") ? "Pharmacy Documentation Team" : "Technical Documentation Team";

      // Create separate rows for each unit
      if (units.length > 0) {
        units.forEach((unit, unitIndex) => {
          data.push({
            area,
            topic,
            topicId: `${baseTopicId}-${String(unitIndex + 1).padStart(2, '0')}`,
            version: "v1.2",
            responsibleDept,
            contentOwner,
            unit,
          });
        });
      } else {
        // If no units, create one row without unit
        data.push({
          area,
          topic,
          topicId: baseTopicId,
          version: "v1.2",
          responsibleDept,
          contentOwner,
          unit: "No units",
        });
      }
    });
  });

  return data;
};

const generateFormTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit: string;
  }> = [];

  Object.entries(formTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = formUnits[topic] || [];
      const baseTopicId = `FORM-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`;
      const responsibleDept = area.includes("Patient") ? "Clinical Services" :
                             area.includes("Medication") ? "Pharmacy Services" : "Technical Services";
      const contentOwner = area.includes("Patient") ? "Clinical Forms Team" :
                          area.includes("Medication") ? "Pharmacy Forms Team" : "Technical Forms Team";

      // Create separate rows for each unit
      if (units.length > 0) {
        units.forEach((unit, unitIndex) => {
          data.push({
            area,
            topic,
            topicId: `${baseTopicId}-${String(unitIndex + 1).padStart(2, '0')}`,
            version: "v2.1",
            responsibleDept,
            contentOwner,
            unit,
          });
        });
      } else {
        // If no units, create one row without unit
        data.push({
          area,
          topic,
          topicId: baseTopicId,
          version: "v2.1",
          responsibleDept,
          contentOwner,
          unit: "No units",
        });
      }
    });
  });

  return data;
};

const generateChecklistTableData = () => {
  const data: Array<{
    area: string;
    topic: string;
    topicId: string;
    version: string;
    responsibleDept: string;
    contentOwner: string;
    unit: string;
  }> = [];

  Object.entries(checklistTopics).forEach(([area, topics]) => {
    topics.forEach((topic, topicIndex) => {
      const units = checklistUnits[topic] || [];
      const baseTopicId = `CHK-${area.substring(0, 3).toUpperCase()}-${String(topicIndex + 1).padStart(3, '0')}`;
      const responsibleDept = area.includes("Patient") ? "Clinical Services" :
                             area.includes("Medication") ? "Pharmacy Services" : "Technical Services";
      const contentOwner = area.includes("Patient") ? "Clinical Safety Team" :
                          area.includes("Medication") ? "Pharmacy Safety Team" : "Technical Safety Team";

      // Create separate rows for each unit
      if (units.length > 0) {
        units.forEach((unit, unitIndex) => {
          data.push({
            area,
            topic,
            topicId: `${baseTopicId}-${String(unitIndex + 1).padStart(2, '0')}`,
            version: "v3.0",
            responsibleDept,
            contentOwner,
            unit,
          });
        });
      } else {
        // If no units, create one row without unit
        data.push({
          area,
          topic,
          topicId: baseTopicId,
          version: "v3.0",
          responsibleDept,
          contentOwner,
          unit: "No units",
        });
      }
    });
  });

  return data;
};

// Assignment Details Table Component with hierarchical structure
interface AssignmentDetailsTableProps {
  assignment: TrainingAssignment;
  users: Array<{
    id: string;
    name: string;
    employeeNo: string;
    email: string;
    department: string;
    division: string;
    type: string;
  }>;
  onExportFilteredData?: (
    filteredData: Array<{
      businessUnit: string;
      departmentGroup: string;
      department: string;
      division: string;
      subDivision: string;
      category: string;
      grade: string;
      designation: string;
      assignedEmployee: string;
      status: string;
      dueDate: string;
    }>
  ) => void;
}

function AssignmentDetailsTable({
  assignment,
  users,
  onExportFilteredData,
}: AssignmentDetailsTableProps) {
  // State for filters
  const [detailFilters, setDetailFilters] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignees: "",
    assignedEmployee: "",
  });

  // Define the row data type
  type AssignmentDetailRow = {
    businessUnit: string;
    division: string;
    departmentGroup: string;
    department: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
    assignees: number;
    assignedEmployee: string;
    isMainRow: boolean;
    hierarchyLevel: "primary" | "secondary";
  };

  // State for filtered rows
  const [detailFilteredRows, setDetailFilteredRows] = useState<
    AssignmentDetailRow[]
  >([]);

  // State to track which filter popover is open
  const [detailOpenPopover, setDetailOpenPopover] = useState<string | null>(
    null
  );

  // State to track selected values from dropdowns
  const [detailSelectedValues, setDetailSelectedValues] = useState({
    businessUnit: "",
    departmentGroup: "",
    department: "",
    division: "",
    subDivision: "",
    category: "",
    grade: "",
    designation: "",
    assignees: "",
    assignedEmployee: "",
  });

  // State for overall table accordion functionality
  const [isTableExpanded, setIsTableExpanded] = useState(true);

  // Function to generate static random employee count based on a seed
  const generateStaticRandomEmployeeCount = (seed: string) => {
    // Simple hash function to convert string to number
    let hash = 0;
    for (let i = 0; i < seed.length; i++) {
      const char = seed.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    // Use absolute value and modulo to get a number between 1 and 15
    return Math.abs(hash % 15) + 1;
  };

  // Generate table rows based on assignment data following the hierarchical structure from the image
  const generateAssignmentDetailRows = () => {
    // Split the comma-separated values to create individual rows
    const businessUnits = assignment.businessUnit.split(", ");
    const divisions = assignment.division.split(", ");
    const departmentGroups = assignment.departmentGroup.split(", ");
    const departments = assignment.department.split(", ");
    const subDivisions = assignment.subDivision.split(", ");
    const categories = assignment.category.split(", ");
    const grades = assignment.grade.split(", ");
    const designations = assignment.designation.split(", ");

    // Create rows based on the actual assignment data structure
    const rows = [];

    // Create combinations based on the organizational hierarchy in the assignment
    // Each combination represents a specific organizational path with assigned employees
    businessUnits.forEach((businessUnit) => {
      departmentGroups.forEach((departmentGroup) => {
        departments.forEach((department) => {
          divisions.forEach((division) => {
            subDivisions.forEach((subDivision) => {
              categories.forEach((category) => {
                grades.forEach((grade) => {
                  designations.forEach((designation) => {
                    // Create a unique seed for this combination to ensure static values
                    const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                    const employeeCount = generateStaticRandomEmployeeCount(seed);

                    // For each organizational combination, create one row with static employee count
                    const row = {
                      businessUnit: businessUnit.trim(),
                      departmentGroup: departmentGroup.trim(),
                      department: department.trim(),
                      division: division.trim(),
                      subDivision: subDivision.trim(),
                      category: category.trim(),
                      grade: grade.trim(),
                      designation: designation.trim(),
                      assignees: employeeCount, // Static random number of assigned employees
                      assignedEmployee: `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`,
                      isMainRow: rows.length === 0, // First row is main row
                      hierarchyLevel:
                        rows.length === 0 ? "primary" : "secondary",
                    };
                    rows.push(row);
                  });
                });
              });
            });
          });
        });
      });
    });

    return rows;
  };

  const tableRows = generateAssignmentDetailRows();

  // Get all unique values for each column from the table rows
  const getUniqueValues = (field: string, rows: AssignmentDetailRow[]) => {
    const values = rows.map((row) => row[field as keyof AssignmentDetailRow]);
    const uniqueValues = [...new Set(values)].filter(Boolean);
    return uniqueValues as string[];
  };

  // Apply filters to rows
  React.useEffect(() => {
    let result = [...tableRows];

    // Apply each filter - either from dropdown or search input
    if (detailSelectedValues.businessUnit) {
      result = result.filter(
        (row) => row.businessUnit === detailSelectedValues.businessUnit
      );
    } else if (detailFilters.businessUnit) {
      result = result.filter((row) =>
        row.businessUnit
          .toLowerCase()
          .includes(detailFilters.businessUnit.toLowerCase())
      );
    }

    if (detailSelectedValues.departmentGroup) {
      result = result.filter(
        (row) => row.departmentGroup === detailSelectedValues.departmentGroup
      );
    } else if (detailFilters.departmentGroup) {
      result = result.filter((row) =>
        row.departmentGroup
          .toLowerCase()
          .includes(detailFilters.departmentGroup.toLowerCase())
      );
    }

    if (detailSelectedValues.department) {
      result = result.filter(
        (row) => row.department === detailSelectedValues.department
      );
    } else if (detailFilters.department) {
      result = result.filter((row) =>
        row.department
          .toLowerCase()
          .includes(detailFilters.department.toLowerCase())
      );
    }

    if (detailSelectedValues.division) {
      result = result.filter(
        (row) => row.division === detailSelectedValues.division
      );
    } else if (detailFilters.division) {
      result = result.filter((row) =>
        row.division
          .toLowerCase()
          .includes(detailFilters.division.toLowerCase())
      );
    }

    if (detailSelectedValues.subDivision) {
      result = result.filter(
        (row) => row.subDivision === detailSelectedValues.subDivision
      );
    } else if (detailFilters.subDivision) {
      result = result.filter((row) =>
        row.subDivision
          .toLowerCase()
          .includes(detailFilters.subDivision.toLowerCase())
      );
    }

    if (detailSelectedValues.category) {
      result = result.filter(
        (row) => row.category === detailSelectedValues.category
      );
    } else if (detailFilters.category) {
      result = result.filter((row) =>
        row.category
          .toLowerCase()
          .includes(detailFilters.category.toLowerCase())
      );
    }

    if (detailSelectedValues.grade) {
      result = result.filter((row) => row.grade === detailSelectedValues.grade);
    } else if (detailFilters.grade) {
      result = result.filter((row) =>
        row.grade.toLowerCase().includes(detailFilters.grade.toLowerCase())
      );
    }

    if (detailSelectedValues.designation) {
      result = result.filter(
        (row) => row.designation === detailSelectedValues.designation
      );
    } else if (detailFilters.designation) {
      result = result.filter((row) =>
        row.designation
          .toLowerCase()
          .includes(detailFilters.designation.toLowerCase())
      );
    }

    if (detailSelectedValues.assignees) {
      result = result.filter(
        (row) =>
          row.assignees !== undefined &&
          row.assignees.toString() === detailSelectedValues.assignees
      );
    } else if (detailFilters.assignees) {
      result = result.filter(
        (row) =>
          row.assignees !== undefined &&
          row.assignees.toString().includes(detailFilters.assignees)
      );
    }

    if (detailSelectedValues.assignedEmployee) {
      result = result.filter(
        (row) => row.assignedEmployee === detailSelectedValues.assignedEmployee
      );
    } else if (detailFilters.assignedEmployee) {
      result = result.filter((row) =>
        row.assignedEmployee
          .toLowerCase()
          .includes(detailFilters.assignedEmployee.toLowerCase())
      );
    }

    setDetailFilteredRows(result);
  }, [tableRows, detailFilters, detailSelectedValues]);

  // Call the export callback whenever filtered data changes
  React.useEffect(() => {
    if (onExportFilteredData) {
      // Convert AssignmentDetailRow to the expected export format
      const exportData = detailFilteredRows.map((row) => ({
        businessUnit: row.businessUnit,
        departmentGroup: row.departmentGroup,
        department: row.department,
        division: row.division,
        subDivision: row.subDivision,
        category: row.category,
        grade: row.grade,
        designation: row.designation,
        assignedEmployee: row.assignedEmployee,
        status: assignment.status,
        dueDate: assignment.dueDate || "N/A",
      }));
      onExportFilteredData(exportData);
    }
  }, [
    detailFilteredRows,
    onExportFilteredData,
    assignment.status,
    assignment.dueDate,
  ]);

  // Function to handle filter changes
  const handleDetailFilterChange = (field: string, value: string) => {
    setDetailFilters((prev) => ({
      ...prev,
      [field]: value,
    }));

    // Clear dropdown selection when using text filter
    if (value) {
      setDetailSelectedValues((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };

  // Function to handle dropdown selection
  const handleDetailDropdownSelect = (field: string, value: string) => {
    // If "all" is selected, clear the filter
    if (value === "all") {
      setDetailSelectedValues((prev) => ({
        ...prev,
        [field]: "",
      }));
    } else {
      setDetailSelectedValues((prev) => ({
        ...prev,
        [field]: value,
      }));
    }

    // Clear the text filter when using dropdown
    setDetailFilters((prev) => ({
      ...prev,
      [field]: "",
    }));
  };

  // Function to clear a specific filter
  const clearDetailFilter = (field: string) => {
    setDetailFilters((prev) => ({
      ...prev,
      [field]: "",
    }));
    setDetailSelectedValues((prev) => ({
      ...prev,
      [field]: "",
    }));
  };

  // Function to clear all filters
  const clearAllDetailFilters = () => {
    setDetailFilters({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignees: "",
      assignedEmployee: "",
    });
    setDetailSelectedValues({
      businessUnit: "",
      departmentGroup: "",
      department: "",
      division: "",
      subDivision: "",
      category: "",
      grade: "",
      designation: "",
      assignees: "",
      assignedEmployee: "",
    });
  };

  // Function to export filtered data to Excel
  const exportFilteredDataToExcel = () => {
    try {
      // Use the filtered rows for export
      const exportData = detailFilteredRows.map((row) => ({
        "Business Unit": row.businessUnit,
        "Department Group": row.departmentGroup,
        Department: row.department,
        Division: row.division,
        "Sub-Division": row.subDivision,
        Category: row.category,
        Grade: row.grade,
        Designation: row.designation,
        "Assigned Employee": row.assignedEmployee,
        Status: assignment.status,
        "Due Date": assignment.dueDate || "N/A",
      }));

      // Export to Excel
      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Filtered Assignment Details");
      XLSX.writeFile(wb, `Filtered_Assignment_Details_${assignment.id}.xlsx`);

      toast({
        title: "Excel Generated",
        description: "Filtered assignment details have been exported to Excel.",
      });
    } catch (error) {
      console.error("Error exporting to Excel:", error);
      toast({
        title: "Error",
        description: "There was an error exporting to Excel. Please try again.",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="overflow-x-auto">
      {isTableExpanded && (
        <Table>
          <TableHeader className="bg-slate-50">
            <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
              <TableHead className="font-medium text-left sticky left-0 z-10 bg-slate-50">
                <div className="flex items-center gap-1">
                  <span>Business Unit</span>
                  <Popover
                    open={detailOpenPopover === "businessUnit"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "businessUnit" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.businessUnit ||
                            detailSelectedValues.businessUnit
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[280px]">
                      <div className="space-y-4">
                        <h4 className="text-sm font-medium">
                          Filter Business Unit
                        </h4>

                        <div className="space-y-3">
                          <div>
                            <label className="text-xs font-medium text-muted-foreground">
                              Select from dropdown
                            </label>
                            <Select
                              value={detailSelectedValues.businessUnit}
                              onValueChange={(value) =>
                                handleDetailDropdownSelect(
                                  "businessUnit",
                                  value
                                )
                              }
                            >
                              <SelectTrigger className="h-9 mt-1">
                                <SelectValue placeholder="Select business unit" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="all">All</SelectItem>
                                {getUniqueValues("businessUnit", tableRows).map(
                                  (value) => (
                                    <SelectItem key={value} value={value}>
                                      {value}
                                    </SelectItem>
                                  )
                                )}
                              </SelectContent>
                            </Select>
                          </div>

                          <div className="text-center text-xs text-muted-foreground">
                            OR
                          </div>

                          <div>
                            <label className="text-xs font-medium text-muted-foreground">
                              Search by text
                            </label>
                            <Input
                              placeholder="Type to search..."
                              value={detailFilters.businessUnit}
                              onChange={(e) =>
                                handleDetailFilterChange(
                                  "businessUnit",
                                  e.target.value
                                )
                              }
                              className="h-9 mt-1"
                            />
                          </div>
                        </div>

                        <div className="flex justify-between pt-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("businessUnit")}
                            className="text-xs px-4"
                            disabled={
                              !detailFilters.businessUnit &&
                              !detailSelectedValues.businessUnit
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs px-4 bg-purple-600 hover:bg-purple-700"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Department Group</span>
                  <Popover
                    open={detailOpenPopover === "departmentGroup"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "departmentGroup" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.departmentGroup ||
                            detailSelectedValues.departmentGroup
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Department Group
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("departmentGroup")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.departmentGroup}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "departmentGroup",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.departmentGroup}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect(
                                "departmentGroup",
                                value
                              )
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues(
                                "departmentGroup",
                                tableRows
                              ).map((value) => (
                                <SelectItem key={value} value={value}>
                                  {value}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("departmentGroup")}
                            className="text-xs"
                            disabled={
                              !detailFilters.departmentGroup &&
                              !detailSelectedValues.departmentGroup
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Department</span>
                  <Popover
                    open={detailOpenPopover === "department"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "department" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.department ||
                            detailSelectedValues.department
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Department
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("department")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.department}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "department",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.department}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("department", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("department", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("department")}
                            className="text-xs"
                            disabled={
                              !detailFilters.department &&
                              !detailSelectedValues.department
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Division</span>
                  <Popover
                    open={detailOpenPopover === "division"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "division" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.division ||
                            detailSelectedValues.division
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Division
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("division")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.division}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "division",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.division}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("division", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("division", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("division")}
                            className="text-xs"
                            disabled={
                              !detailFilters.division &&
                              !detailSelectedValues.division
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Sub-Division</span>
                  <Popover
                    open={detailOpenPopover === "subDivision"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "subDivision" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.subDivision ||
                            detailSelectedValues.subDivision
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Sub-Division
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("subDivision")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.subDivision}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "subDivision",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.subDivision}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("subDivision", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("subDivision", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("subDivision")}
                            className="text-xs"
                            disabled={
                              !detailFilters.subDivision &&
                              !detailSelectedValues.subDivision
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Category</span>
                  <Popover
                    open={detailOpenPopover === "category"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "category" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.category ||
                            detailSelectedValues.category
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Category
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("category")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.category}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "category",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.category}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("category", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("category", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("category")}
                            className="text-xs"
                            disabled={
                              !detailFilters.category &&
                              !detailSelectedValues.category
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Grade</span>
                  <Popover
                    open={detailOpenPopover === "grade"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "grade" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.grade || detailSelectedValues.grade
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">Filter Grade</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("grade")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.grade}
                            onChange={(e) =>
                              handleDetailFilterChange("grade", e.target.value)
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.grade}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("grade", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("grade", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("grade")}
                            className="text-xs"
                            disabled={
                              !detailFilters.grade &&
                              !detailSelectedValues.grade
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-left">
                <div className="flex items-center gap-1">
                  <span>Designation</span>
                  <Popover
                    open={detailOpenPopover === "designation"}
                    onOpenChange={(open) =>
                      setDetailOpenPopover(open ? "designation" : null)
                    }
                  >
                    <PopoverTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-6 w-6 p-0"
                      >
                        <Filter
                          size={12}
                          className={`${
                            detailFilters.designation ||
                            detailSelectedValues.designation
                              ? "text-primary"
                              : "text-muted-foreground"
                          }`}
                        />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent align="start" className="w-[250px]">
                      <div className="space-y-3">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium">
                            Filter Designation
                          </h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => clearDetailFilter("designation")}
                            className="h-6 w-6 p-0"
                          >
                            <X size={12} />
                          </Button>
                        </div>

                        <div className="space-y-2">
                          <Input
                            placeholder="Search..."
                            value={detailFilters.designation}
                            onChange={(e) =>
                              handleDetailFilterChange(
                                "designation",
                                e.target.value
                              )
                            }
                            className="h-8"
                          />

                          <Select
                            value={detailSelectedValues.designation}
                            onValueChange={(value) =>
                              handleDetailDropdownSelect("designation", value)
                            }
                          >
                            <SelectTrigger className="h-8">
                              <SelectValue placeholder="Select option" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="all">All</SelectItem>
                              {getUniqueValues("designation", tableRows).map(
                                (value) => (
                                  <SelectItem key={value} value={value}>
                                    {value}
                                  </SelectItem>
                                )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="flex justify-between">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => clearDetailFilter("designation")}
                            className="text-xs"
                            disabled={
                              !detailFilters.designation &&
                              !detailSelectedValues.designation
                            }
                          >
                            Clear
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => setDetailOpenPopover(null)}
                            className="text-xs"
                          >
                            Apply
                          </Button>
                        </div>
                      </div>
                    </PopoverContent>
                  </Popover>
                </div>
              </TableHead>

              <TableHead className="font-medium text-center">
                <span>No. of Employee</span>
              </TableHead>

              <TableHead className="font-medium text-center">
                <span>Unassign</span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {detailFilteredRows.length > 0 ? (
              detailFilteredRows.map((row, index) => (
                <TableRow key={index} className="hover:bg-muted/30">
                  <TableCell className="sticky left-0 z-10 font-medium bg-white">
                    {row.businessUnit}
                  </TableCell>
                  <TableCell>{row.departmentGroup}</TableCell>
                  <TableCell>{row.department}</TableCell>
                  <TableCell>{row.division}</TableCell>
                  <TableCell>{row.subDivision}</TableCell>
                  <TableCell>{row.category}</TableCell>
                  <TableCell>{row.grade}</TableCell>
                  <TableCell>{row.designation}</TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="link"
                      className="p-0 h-auto font-medium text-blue-600 hover:text-blue-800 hover:underline"
                      onClick={() => handleEmployeeCountClick(row)}
                    >
                      {row.assignees}
                    </Button>
                  </TableCell>
                  <TableCell className="text-center">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-destructive hover:text-destructive hover:bg-destructive/10"
                      onClick={() => {
                        // Handle unassign functionality here
                        toast({
                          title: "Unassigned",
                          description: "Assignment has been removed.",
                          variant: "destructive",
                        });
                      }}
                      title="Unassign this employee"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={11} className="h-24 text-center">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <Filter className="h-8 w-8 mb-2 opacity-40" />
                    <p className="text-sm font-medium">No results found</p>
                    <p className="text-xs mt-1">
                      Try adjusting your filters or clear them to see all data
                    </p>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearAllDetailFilters}
                      className="mt-4 text-xs"
                    >
                      Clear All Filters
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      )}
    </div>
  );
}

export default function EmployeeTrainingAssignment() {
  const navigate = useNavigate();

  // State for training assignments with sample data
  const [trainingAssignments, setTrainingAssignments] = useState<
    TrainingAssignment[]
  >([
    {
      id: "1",
      employeeIds: ["1", "2"],
      employeeName: "2 Employees",
      trainingProgram:
        "Clinical → Patient Care → Basic Patient Care | Clinical → Nursing → Basic Nursing",
      businessUnit: "KCN, KTN",
      departmentGroup: "Clinical Services, Emergency Services",
      department: "Nursing, Intensive Care",
      division: "Patient Care, Specialized Nursing",
      subDivision: "Intensive Care, General Care",
      category: "Clinical, Critical Care",
      grade: "Junior, Mid-level",
      designation: "Nurse Assistant, Junior Technician",
      status: "Not Started",
      assignmentStatus: "Active",
      assignedDate: "2024-01-15",
      dueDate: "2024-02-15",
      notes: "Initial training assignment for new hires in clinical services.",
    },
    {
      id: "2",
      employeeIds: ["3"],
      employeeName: "Taylor Brown",
      trainingProgram: "Administrative → HR Management → Recruitment",
      businessUnit: "KTN",
      departmentGroup: "Clinical Services",
      department: "General Medicine",
      division: "Internal Medicine",
      subDivision: "Cardiology",
      category: "Interventional Cardiology",
      grade: "Junior",
      designation: "Nurse Assistant",
      status: "In Progress",
      assignmentStatus: "Suspended",
      assignedDate: "2024-01-10",
      dueDate: "2024-02-10",
      notes: "Advanced training for experienced staff members.",
    },
    {
      id: "3",
      employeeIds: ["e1", "e2"],
      employeeName: "2 Employees",
      trainingProgram:
        "Technical → IT Systems → EHR Systems | Technical → Medical Equipment → Equipment Operation",
      businessUnit: "KCN",
      departmentGroup: "Clinical Services",
      department: "Surgery",
      division: "Internal Medicine",
      subDivision: "Cardiology",
      category: "Interventional Cardiology",
      grade: "Junior",
      designation: "Junior Technician",
      status: "Completed",
      assignmentStatus: "Active",
      assignedDate: "2024-01-05",
      dueDate: "2024-01-20",
      notes:
        "Completed technical training for IT systems and medical equipment.",
    },
    {
      id: "4",
      employeeIds: ["3", "4"],
      employeeName: "2 Employees",
      trainingProgram:
        "Patient Safety Checklist → Admission Form → Patient Identification Document | Patient Safety Checklist → Discharge Form → Discharge Summary Document",
      businessUnit: "KCN, KTN",
      departmentGroup: "Clinical Services",
      department: "Nursing",
      division: "Patient Care",
      subDivision: "General Care",
      category: "Clinical",
      grade: "Mid-level",
      designation: "Nurse",
      status: "In Progress",
      assignmentStatus: "Active",
      assignedDate: "2024-01-10",
      dueDate: "2024-02-10",
      notes: "CFD assignment for patient safety documentation.",
    },
    {
      id: "5",
      employeeIds: ["5", "6"],
      employeeName: "2 Employees",
      trainingProgram:
        "Medication Checklist → Prescription Form → Medication Order Document | Medication Checklist → Administration Form → Administration Schedule Document",
      businessUnit: "KTN",
      departmentGroup: "Clinical Services",
      department: "Pharmacy",
      division: "Medication Management",
      subDivision: "Prescription Services",
      category: "Clinical",
      grade: "Senior",
      designation: "Pharmacist",
      status: "Not Started",
      assignmentStatus: "Active",
      assignedDate: "2024-01-12",
      dueDate: "2024-02-12",
      notes: "CFD assignment for medication management documentation.",
    },
    {
      id: "6",
      employeeIds: ["7", "8"],
      employeeName: "2 Employees",
      trainingProgram:
        "Equipment Checklist → Maintenance Form → Maintenance Schedule Document | Equipment Checklist → Calibration Form → Calibration Procedure Document",
      businessUnit: "KCN",
      departmentGroup: "Technical Services",
      department: "Engineering",
      division: "Equipment Management",
      subDivision: "Maintenance",
      category: "Technical",
      grade: "Junior",
      designation: "Technician",
      status: "In Progress",
      assignmentStatus: "Active",
      assignedDate: "2024-01-08",
      dueDate: "2024-02-08",
      notes: "CFD assignment for equipment maintenance documentation.",
    },
  ]);

  // State for training program details dialog
  const [programDetailsOpen, setProgramDetailsOpen] = useState(false);
  const [selectedProgram, setSelectedProgram] = useState<string>("");

  // State for assignment details dialog
  const [detailsDialogOpen, setDetailsDialogOpen] = useState(false);
  const [selectedAssignment, setSelectedAssignment] =
    useState<TrainingAssignment | null>(null);

  // State for edit dialog
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [assignmentToEdit, setAssignmentToEdit] =
    useState<TrainingAssignment | null>(null);

  // State for user details dialog
  const [userDetailsDialogOpen, setUserDetailsDialogOpen] = useState(false);
  const [selectedUserDetails, setSelectedUserDetails] = useState<{
    count: number;
    organizationInfo: {
      businessUnit: string;
      departmentGroup: string;
      department: string;
      division: string;
      subDivision: string;
      category: string;
      grade: string;
      designation: string;
    };
    users: Array<{
      id: string;
      name: string;
      employeeNo: string;
      email: string;
      department: string;
      division: string;
      status: string;
      assignedDate: string;
      dueDate: string;
    }>;
  } | null>(null);

  // State for expanded rows in accordion
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const [expandedAssignmentDetails, setExpandedAssignmentDetails] = useState<
    Set<string>
  >(new Set());

  // State to store filtered data for each assignment (for export functionality)
  const [assignmentFilteredData, setAssignmentFilteredData] = useState<
    Record<
      string,
      Array<{
        businessUnit: string;
        departmentGroup: string;
        department: string;
        division: string;
        subDivision: string;
        category: string;
        grade: string;
        designation: string;
        assignedEmployee: string;
        status: string;
        dueDate: string;
      }>
    >
  >({});

  // State for active tab - default to Training Library & Assignment
  const [activeTab, setActiveTab] = useState("training-library");

  // State for assignment view dialogs
  const [learningAssignmentDialogOpen, setLearningAssignmentDialogOpen] = useState(false);
  const [cfdAssignmentDialogOpen, setCfdAssignmentDialogOpen] = useState(false);

  // State for library tabs content
  // Training Library states
  const [trainingSearchTerm, setTrainingSearchTerm] = useState("");
  const [trainingSelectedArea, setTrainingSelectedArea] = useState("");
  const [trainingFilters, setTrainingFilters] = useState({
    area: "",
    topic: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [trainingSelectedValues, setTrainingSelectedValues] = useState({
    area: "",
    topic: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [trainingOpenPopover, setTrainingOpenPopover] = useState<string | null>(null);
  const [trainingUserSelectionOpen, setTrainingUserSelectionOpen] = useState(false);
  const [trainingSelectedItemForAssignment, setTrainingSelectedItemForAssignment] = useState<any>(null);
  const [trainingAssignedUsers, setTrainingAssignedUsers] = useState<Record<string, Array<any>>>({});
  const [trainingViewAssignmentOpen, setTrainingViewAssignmentOpen] = useState(false);
  const [trainingSelectedAssignmentItem, setTrainingSelectedAssignmentItem] = useState<any>(null);

  // Document Library states
  const [docSearchTerm, setDocSearchTerm] = useState("");
  const [docSelectedArea, setDocSelectedArea] = useState("");
  const [docFilters, setDocFilters] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [docSelectedValues, setDocSelectedValues] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [docOpenPopover, setDocOpenPopover] = useState<string | null>(null);
  const [docUserSelectionOpen, setDocUserSelectionOpen] = useState(false);
  const [docSelectedItemForAssignment, setDocSelectedItemForAssignment] = useState<any>(null);
  const [docAssignedUsers, setDocAssignedUsers] = useState<Record<string, Array<any>>>({});
  const [docViewAssignmentOpen, setDocViewAssignmentOpen] = useState(false);
  const [docSelectedAssignmentItem, setDocSelectedAssignmentItem] = useState<any>(null);

  // Forms Library states
  const [formsSearchTerm, setFormsSearchTerm] = useState("");
  const [formsSelectedArea, setFormsSelectedArea] = useState("");
  const [formsFilters, setFormsFilters] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [formsSelectedValues, setFormsSelectedValues] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [formsOpenPopover, setFormsOpenPopover] = useState<string | null>(null);
  const [formsUserSelectionOpen, setFormsUserSelectionOpen] = useState(false);
  const [formsSelectedItemForAssignment, setFormsSelectedItemForAssignment] = useState<any>(null);
  const [formsAssignedUsers, setFormsAssignedUsers] = useState<Record<string, Array<any>>>({});
  const [formsViewAssignmentOpen, setFormsViewAssignmentOpen] = useState(false);
  const [formsSelectedAssignmentItem, setFormsSelectedAssignmentItem] = useState<any>(null);

  // Checklist Library states
  const [checklistSearchTerm, setChecklistSearchTerm] = useState("");
  const [checklistSelectedArea, setChecklistSelectedArea] = useState("");
  const [checklistFilters, setChecklistFilters] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [checklistSelectedValues, setChecklistSelectedValues] = useState({
    area: "",
    topic: "",
    unit: "",
    topicId: "",
    version: "",
    responsibleDept: "",
    contentOwner: ""
  });
  const [checklistOpenPopover, setChecklistOpenPopover] = useState<string | null>(null);
  const [checklistUserSelectionOpen, setChecklistUserSelectionOpen] = useState(false);
  const [checklistSelectedItemForAssignment, setChecklistSelectedItemForAssignment] = useState<any>(null);
  const [checklistAssignedUsers, setChecklistAssignedUsers] = useState<Record<string, Array<any>>>({});
  const [checklistViewAssignmentOpen, setChecklistViewAssignmentOpen] = useState(false);
  const [checklistSelectedAssignmentItem, setChecklistSelectedAssignmentItem] = useState<any>(null);

  // Handler functions for library assignments
  const handleTrainingUserAssignment = async (selectedUsers: any[]) => {
    if (!trainingSelectedItemForAssignment) return;

    const itemKey = trainingSelectedItemForAssignment.topicId;
    const existingUsers = trainingAssignedUsers[itemKey] || [];
    const newUsers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
    }));

    const existingUserIds = new Set(existingUsers.map(user => user.id));
    const usersToAdd = newUsers.filter(user => !existingUserIds.has(user.id));
    const alreadyAssignedCount = selectedUsers.length - usersToAdd.length;
    const updatedUsers = [...existingUsers, ...usersToAdd];

    setTrainingAssignedUsers(prev => ({
      ...prev,
      [itemKey]: updatedUsers
    }));

    if (usersToAdd.length > 0 && alreadyAssignedCount > 0) {
      toast({
        title: "Assignment updated",
        description: `Added ${usersToAdd.length} new user(s). ${alreadyAssignedCount} user(s) were already assigned.`,
      });
    } else if (usersToAdd.length > 0) {
      toast({
        title: "Assignment successful",
        description: `Successfully assigned ${usersToAdd.length} user(s) to ${trainingSelectedItemForAssignment.topic}.`,
      });
    } else {
      toast({
        title: "No new assignments",
        description: "All selected users were already assigned to this item.",
        variant: "default",
      });
    }
  };

  const handleDocUserAssignment = async (selectedUsers: any[]) => {
    if (!docSelectedItemForAssignment) return;

    const itemKey = docSelectedItemForAssignment.topicId;
    const existingUsers = docAssignedUsers[itemKey] || [];
    const newUsers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
    }));

    const existingUserIds = new Set(existingUsers.map(user => user.id));
    const usersToAdd = newUsers.filter(user => !existingUserIds.has(user.id));
    const updatedUsers = [...existingUsers, ...usersToAdd];

    setDocAssignedUsers(prev => ({
      ...prev,
      [itemKey]: updatedUsers
    }));

    toast({
      title: "Assignment successful",
      description: `Successfully assigned ${usersToAdd.length} user(s) to ${docSelectedItemForAssignment.topic}.`,
    });
  };

  const handleFormsUserAssignment = async (selectedUsers: any[]) => {
    if (!formsSelectedItemForAssignment) return;

    const itemKey = formsSelectedItemForAssignment.topicId;
    const existingUsers = formsAssignedUsers[itemKey] || [];
    const newUsers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
    }));

    const existingUserIds = new Set(existingUsers.map(user => user.id));
    const usersToAdd = newUsers.filter(user => !existingUserIds.has(user.id));
    const updatedUsers = [...existingUsers, ...usersToAdd];

    setFormsAssignedUsers(prev => ({
      ...prev,
      [itemKey]: updatedUsers
    }));

    toast({
      title: "Assignment successful",
      description: `Successfully assigned ${usersToAdd.length} user(s) to ${formsSelectedItemForAssignment.topic}.`,
    });
  };

  const handleChecklistUserAssignment = async (selectedUsers: any[]) => {
    if (!checklistSelectedItemForAssignment) return;

    const itemKey = checklistSelectedItemForAssignment.topicId;
    const existingUsers = checklistAssignedUsers[itemKey] || [];
    const newUsers = selectedUsers.map(user => ({
      id: user.id,
      name: user.name,
      employeeNo: user.employeeNo,
      email: user.email,
      department: user.department,
      division: user.division,
    }));

    const existingUserIds = new Set(existingUsers.map(user => user.id));
    const usersToAdd = newUsers.filter(user => !existingUserIds.has(user.id));
    const updatedUsers = [...existingUsers, ...usersToAdd];

    setChecklistAssignedUsers(prev => ({
      ...prev,
      [itemKey]: updatedUsers
    }));

    toast({
      title: "Assignment successful",
      description: `Successfully assigned ${usersToAdd.length} user(s) to ${checklistSelectedItemForAssignment.topic}.`,
    });
  };

  // Assignment handler functions for all libraries
  const handleTrainingAssignUsers = (item: any) => {
    setTrainingSelectedItemForAssignment(item);
    setTrainingUserSelectionOpen(true);
  };

  const handleDocAssignUsers = (item: any) => {
    setDocSelectedItemForAssignment(item);
    setDocUserSelectionOpen(true);
  };

  const handleFormsAssignUsers = (item: any) => {
    setFormsSelectedItemForAssignment(item);
    setFormsUserSelectionOpen(true);
  };

  const handleChecklistAssignUsers = (item: any) => {
    setChecklistSelectedItemForAssignment(item);
    setChecklistUserSelectionOpen(true);
  };

  // Display functions for assigned users in all libraries
  const getTrainingAssignedUsersDisplay = (topicId: string) => {
    const users = trainingAssignedUsers[topicId] || [];
    if (users.length === 0) return "No assignments";
    if (users.length === 1) return users[0].name;
    return `${users[0].name} +${users.length - 1} more`;
  };

  const getDocAssignedUsersDisplay = (topicId: string) => {
    const users = docAssignedUsers[topicId] || [];
    if (users.length === 0) return "No assignments";
    if (users.length === 1) return users[0].name;
    return `${users[0].name} +${users.length - 1} more`;
  };

  const getFormsAssignedUsersDisplay = (topicId: string) => {
    const users = formsAssignedUsers[topicId] || [];
    if (users.length === 0) return "No assignments";
    if (users.length === 1) return users[0].name;
    return `${users[0].name} +${users.length - 1} more`;
  };

  const getChecklistAssignedUsersDisplay = (topicId: string) => {
    const users = checklistAssignedUsers[topicId] || [];
    if (users.length === 0) return "No assignments";
    if (users.length === 1) return users[0].name;
    return `${users[0].name} +${users.length - 1} more`;
  };

  // Function to toggle row expansion
  const toggleRowExpansion = (assignmentId: string) => {
    const newExpandedRows = new Set(expandedRows);
    if (newExpandedRows.has(assignmentId)) {
      newExpandedRows.delete(assignmentId);
    } else {
      newExpandedRows.add(assignmentId);
    }
    setExpandedRows(newExpandedRows);
  };

  // Function to generate mock user data for a specific organizational combination
  const generateMockUserData = (count: number, organizationInfo: {
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
  }) => {
    const firstNames = [
      "John", "Jane", "Michael", "Sarah", "David", "Emily", "Robert", "Lisa",
      "James", "Maria", "William", "Jennifer", "Richard", "Patricia", "Charles",
      "Linda", "Joseph", "Barbara", "Thomas", "Elizabeth", "Christopher", "Susan",
      "Daniel", "Jessica", "Matthew", "Karen", "Anthony", "Nancy", "Mark", "Betty"
    ];

    const lastNames = [
      "Smith", "Johnson", "Williams", "Brown", "Jones", "Garcia", "Miller", "Davis",
      "Rodriguez", "Martinez", "Hernandez", "Lopez", "Gonzalez", "Wilson", "Anderson",
      "Thomas", "Taylor", "Moore", "Jackson", "Martin", "Lee", "Perez", "Thompson",
      "White", "Harris", "Sanchez", "Clark", "Ramirez", "Lewis", "Robinson"
    ];

    const statuses = ["Active", "In Progress", "Completed", "Not Started"];

    const users = [];
    for (let i = 0; i < count; i++) {
      const firstName = firstNames[i % firstNames.length];
      const lastName = lastNames[(i + count) % lastNames.length];
      const name = `${firstName} ${lastName}`;

      // Generate a unique seed for this user to ensure consistent data
      const userSeed = `${organizationInfo.businessUnit}-${organizationInfo.departmentGroup}-${name}-${i}`;
      let hash = 0;
      for (let j = 0; j < userSeed.length; j++) {
        const char = userSeed.charCodeAt(j);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash;
      }

      const employeeNo = `EMP-${String(Math.abs(hash % 9999) + 1000).padStart(4, '0')}`;
      const email = `${firstName.toLowerCase()}.${lastName.toLowerCase()}@company.com`;
      const status = statuses[Math.abs(hash) % statuses.length];

      // Generate dates
      const assignedDate = new Date(2024, 0, Math.abs(hash % 28) + 1).toISOString().split('T')[0];
      const dueDate = new Date(2024, 1, Math.abs(hash % 28) + 1).toISOString().split('T')[0];

      users.push({
        id: `user-${hash}-${i}`,
        name,
        employeeNo,
        email,
        department: organizationInfo.department,
        division: organizationInfo.division,
        status,
        assignedDate,
        dueDate
      });
    }

    return users;
  };

  // Function to handle employee count click - supports both row types
  const handleEmployeeCountClick = (row: {
    assignees?: number;
    employeeCount?: number;
    businessUnit: string;
    departmentGroup: string;
    department: string;
    division: string;
    subDivision: string;
    category: string;
    grade: string;
    designation: string;
  }) => {
    const count = row.assignees || row.employeeCount || 0;
    const mockUsers = generateMockUserData(count, {
      businessUnit: row.businessUnit,
      departmentGroup: row.departmentGroup,
      department: row.department,
      division: row.division,
      subDivision: row.subDivision,
      category: row.category,
      grade: row.grade,
      designation: row.designation
    });

    setSelectedUserDetails({
      count: count,
      organizationInfo: {
        businessUnit: row.businessUnit,
        departmentGroup: row.departmentGroup,
        department: row.department,
        division: row.division,
        subDivision: row.subDivision,
        category: row.category,
        grade: row.grade,
        designation: row.designation
      },
      users: mockUsers
    });

    setUserDetailsDialogOpen(true);
  };

  // Function to download user details as Excel
  const downloadUserDetails = () => {
    if (!selectedUserDetails) return;

    try {
      const exportData = selectedUserDetails.users.map((user) => ({
        "Employee No": user.employeeNo,
        "Name": user.name,
        "Email": user.email,
        "Department": user.department,
        "Division": user.division,
        "Status": user.status,
        "Business Unit": selectedUserDetails.organizationInfo.businessUnit,
        "Department Group": selectedUserDetails.organizationInfo.departmentGroup,
        "Sub-Division": selectedUserDetails.organizationInfo.subDivision,
        "Category": selectedUserDetails.organizationInfo.category,
        "Grade": selectedUserDetails.organizationInfo.grade,
        "Designation": selectedUserDetails.organizationInfo.designation
      }));

      const ws = XLSX.utils.json_to_sheet(exportData);
      const wb = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(wb, ws, "Employee Details");

      const fileName = `Employee_Details_${selectedUserDetails.organizationInfo.businessUnit}_${selectedUserDetails.organizationInfo.departmentGroup}_${new Date().toISOString().split('T')[0]}.xlsx`;
      XLSX.writeFile(wb, fileName);

      toast({
        title: "Download Complete",
        description: `Employee details have been downloaded as ${fileName}`,
      });
    } catch (error) {
      console.error("Error downloading user details:", error);
      toast({
        title: "Error",
        description: "There was an error downloading the employee details. Please try again.",
        variant: "destructive",
      });
    }
  };

  const [searchTerm, setSearchTerm] = useState("");
  const [dialogOpen, setDialogOpen] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [currentStep, setCurrentStep] = useState(1);
  const [assignmentType, setAssignmentType] = useState<"Training" | "CFD">(
    "Training"
  );
  const [filters, setFilters] = useState({
    businessUnit: [] as string[],
    departmentGroup: [] as string[],
    department: [] as string[],
    division: [] as string[],
    subDivision: [] as string[],
    category: [] as string[],
    grade: [] as string[],
    designation: [] as string[],
    assignmentStatus: [] as string[],
  });

  // State to track which filter dropdown is open
  const [openFilterDropdown, setOpenFilterDropdown] = useState<string | null>(
    null
  );

  // State for dialog form fields
  const [notes, setNotes] = useState("");
  const [selectedEmployeeIds, setSelectedEmployeeIds] = useState<string[]>([]);

  // Mock users data (in a real app, this would be fetched from an API)
  const [users, setUsers] = useState([
    // Internal users
    {
      id: "1",
      name: "Alex Johnson",
      employeeNo: "EMP-001",
      email: "<EMAIL>",
      department: "Engineering",
      division: "Technology",
      type: "internal",
    },
    {
      id: "2",
      name: "Jamie Smith",
      employeeNo: "EMP-002",
      email: "<EMAIL>",
      department: "Finance",
      division: "Business",
      type: "internal",
    },
    {
      id: "3",
      name: "Taylor Brown",
      employeeNo: "EMP-003",
      email: "<EMAIL>",
      department: "Product",
      division: "Technology",
      type: "internal",
    },
    // External users
    {
      id: "e1",
      name: "Robin Lee",
      employeeNo: "EXT-001",
      email: "<EMAIL>",
      department: "Research",
      division: "Healthcare",
      type: "external",
    },
    {
      id: "e2",
      name: "Sam Green",
      employeeNo: "EXT-002",
      email: "<EMAIL>",
      department: "Training",
      division: "Education",
      type: "external",
    },
  ]);

  // Get the selected employees
  const selectedEmployees =
    selectedEmployeeIds.length > 0
      ? users.filter((user) => selectedEmployeeIds.includes(user.id))
      : [];

  // Initialize any existing assignments with employeeIds if they don't have it
  useEffect(() => {
    setTrainingAssignments((prevAssignments) =>
      prevAssignments.map((assignment) => {
        if (!assignment.employeeIds) {
          return {
            ...assignment,
            employeeIds: [
              users.find((u) => u.name === assignment.employeeName)?.id || "1",
            ],
          };
        }
        return assignment;
      })
    );
  }, [users]);

  // State for dynamic hierarchies
  const [areas, setAreas] = useState<string[]>(trainingAreas);
  const [topics, setTopics] =
    useState<Record<string, string[]>>(trainingTopics);
  const [units, setUnits] = useState<Record<string, string[]>>(trainingUnits);

  // Effect for assignment type change
  useEffect(() => {
    // Reset selections when assignment type changes
    setSelectedArea("");
    setSelectedTopic("");
    setSelectedUnits([]);
    setAvailableTopics([]);
    setAvailableUnits([]);
    setAllUnitsSelected(false);
    setProgramSearchTerm(""); // Reset program search term

    // Update the hierarchies based on assignment type
    if (assignmentType === "Training") {
      setAreas(trainingAreas);
      setTopics(trainingTopics);
      setUnits(trainingUnits);
      // Set all training areas as expanded by default
      setExpandedAreas([...trainingAreas]);
    } else {
      setAreas(cfdAreas);
      setTopics(cfdTopics);
      setUnits(cfdUnits);
      // Set all CFD areas as expanded by default
      setExpandedAreas([...cfdAreas]);
    }
  }, [assignmentType]);

  // Area → Topic → Unit hierarchy state
  const [selectedArea, setSelectedArea] = useState("");
  const [selectedTopic, setSelectedTopic] = useState("");
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [selectedTopics, setSelectedTopics] = useState<
    { area: string; topic: string }[]
  >([]);
  const [selectedCfdUnits, setSelectedCfdUnits] = useState<
    { area: string; topic: string; unit: string }[]
  >([]);
  const [availableTopics, setAvailableTopics] = useState<string[]>([]);
  const [availableUnits, setAvailableUnits] = useState<string[]>([]);
  const [allUnitsSelected, setAllUnitsSelected] = useState(false);
  const [programSearchTerm, setProgramSearchTerm] = useState("");
  // State to track expanded areas in the accordion - initialize with all areas expanded
  const [expandedAreas, setExpandedAreas] = useState<string[]>(
    assignmentType === "Training" ? trainingAreas : cfdAreas
  );

  // State for assigned area and topic dropdowns
  const [selectedAssignedArea, setSelectedAssignedArea] = useState("");
  const [selectedAssignedTopic, setSelectedAssignedTopic] = useState("");

  // State for CFD Topic Assignment Details
  const [selectedCfdArea, setSelectedCfdArea] = useState("");
  const [selectedCfdTopic, setSelectedCfdTopic] = useState("");
  const [selectedCfdUnit, setSelectedCfdUnit] = useState("");
  const [cfdCurrentPage, setCfdCurrentPage] = useState(1);
  const [cfdItemsPerPage] = useState(5);

  // Function to get all units from all areas and topics based on assignment type
  const getAllUnits = () => {
    const allUnits: string[] = [];
    const currentUnits =
      assignmentType === "Training" ? trainingUnits : cfdUnits;

    Object.values(currentUnits).forEach((unitArray) => {
      unitArray.forEach((unit) => {
        if (!allUnits.includes(unit)) {
          allUnits.push(unit);
        }
      });
    });
    return allUnits;
  };

  // Function to filter areas, topics, and units based on search term
  const filterProgramItems = (searchTerm: string) => {
    if (!searchTerm.trim()) {
      return {
        filteredAreas: assignmentType === "Training" ? trainingAreas : cfdAreas,
        filteredTopics:
          assignmentType === "Training" ? trainingTopics : cfdTopics,
        filteredUnits: assignmentType === "Training" ? trainingUnits : cfdUnits,
        matchCounts: { areas: 0, topics: 0, units: 0 },
      };
    }

    const currentAreas =
      assignmentType === "Training" ? trainingAreas : cfdAreas;
    const currentTopics =
      assignmentType === "Training" ? trainingTopics : cfdTopics;
    const currentUnits =
      assignmentType === "Training" ? trainingUnits : cfdUnits;

    const searchTermLower = searchTerm.toLowerCase();

    // Find matching areas
    const matchingAreas = currentAreas.filter((area) =>
      area.toLowerCase().includes(searchTermLower)
    );

    // Find matching topics
    const matchingTopicsByArea: Record<string, string[]> = {};
    const allMatchingTopics: string[] = [];

    Object.entries(currentTopics).forEach(([area, topicList]) => {
      const matchingTopics = topicList.filter((topic) =>
        topic.toLowerCase().includes(searchTermLower)
      );

      if (matchingTopics.length > 0 || matchingAreas.includes(area)) {
        matchingTopicsByArea[area] =
          matchingTopics.length > 0 ? matchingTopics : topicList;
        matchingTopics.forEach((topic) => {
          if (!allMatchingTopics.includes(topic)) {
            allMatchingTopics.push(topic);
          }
        });
      }
    });

    // Find matching units
    const matchingUnitsByTopic: Record<string, string[]> = {};
    const allMatchingUnits: string[] = [];

    Object.entries(currentUnits).forEach(([topic, unitList]) => {
      const matchingUnits = unitList.filter((unit) =>
        unit.toLowerCase().includes(searchTermLower)
      );

      if (matchingUnits.length > 0 || allMatchingTopics.includes(topic)) {
        matchingUnitsByTopic[topic] =
          matchingUnits.length > 0 ? matchingUnits : unitList;
        matchingUnits.forEach((unit) => {
          if (!allMatchingUnits.includes(unit)) {
            allMatchingUnits.push(unit);
          }
        });
      }
    });

    // Add areas that have matching topics or units
    const filteredAreas = currentAreas.filter((area) => {
      // Include if area matches search
      if (matchingAreas.includes(area)) return true;

      // Include if any of its topics match search
      const areaTopics = currentTopics[area] || [];
      if (areaTopics.some((topic) => allMatchingTopics.includes(topic)))
        return true;

      // Include if any of its topics have matching units
      return areaTopics.some((topic) => {
        const topicUnits = currentUnits[topic] || [];
        return topicUnits.some((unit) => allMatchingUnits.includes(unit));
      });
    });

    // Create filtered topics object
    const filteredTopics: Record<string, string[]> = {};
    filteredAreas.forEach((area) => {
      const areaTopics = currentTopics[area] || [];
      filteredTopics[area] = areaTopics.filter((topic) => {
        // Include if topic matches search
        if (allMatchingTopics.includes(topic)) return true;

        // Include if any of its units match search
        const topicUnits = currentUnits[topic] || [];
        return topicUnits.some((unit) => allMatchingUnits.includes(unit));
      });
    });

    // Create filtered units object
    const filteredUnits: Record<string, string[]> = {};
    Object.keys(filteredTopics).forEach((area) => {
      filteredTopics[area].forEach((topic) => {
        const topicUnits = currentUnits[topic] || [];
        filteredUnits[topic] = topicUnits.filter((unit) => {
          // Include if unit matches search or if we're showing all units for this topic
          return (
            allMatchingUnits.includes(unit) || allMatchingTopics.includes(topic)
          );
        });
      });
    });

    return {
      filteredAreas,
      filteredTopics,
      filteredUnits,
      matchCounts: {
        areas: matchingAreas.length,
        topics: allMatchingTopics.length,
        units: allMatchingUnits.length,
      },
    };
  };

  // Business Unit multi-select state
  const [selectedBusinessUnits, setSelectedBusinessUnits] = useState<string[]>(
    []
  );

  // Organizational hierarchy state
  const [selectedDepartmentGroups, setSelectedDepartmentGroups] = useState<
    string[]
  >([]);
  const [selectedDepartments, setSelectedDepartments] = useState<string[]>([]);
  const [selectedDivisions, setSelectedDivisions] = useState<string[]>([]);
  const [selectedSubDivisions, setSelectedSubDivisions] = useState<string[]>(
    []
  );
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedGrades, setSelectedGrades] = useState<string[]>([]);
  const [selectedDesignations, setSelectedDesignations] = useState<string[]>(
    []
  );

  // Available options based on selections
  const [availableDepartments, setAvailableDepartments] = useState<string[]>(
    []
  );
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [availableSubDivisions, setAvailableSubDivisions] = useState<string[]>(
    []
  );
  const [availableCategories, setAvailableCategories] = useState<string[]>([]);
  const [availableGrades, setAvailableGrades] = useState<string[]>([]);
  const [availableDesignations, setAvailableDesignations] = useState<string[]>(
    []
  );

  // We'll use direct preview in the UI instead of a separate state

  // Effect for Area → Topic → Unit hierarchy
  useEffect(() => {
    if (selectedArea) {
      const currentTopics =
        assignmentType === "Training" ? trainingTopics : cfdTopics;
      setAvailableTopics(
        currentTopics[selectedArea as keyof typeof currentTopics] || []
      );
      setSelectedTopic("");
      setSelectedUnits([]);
      setAvailableUnits([]);
    } else {
      setAvailableTopics([]);
    }
  }, [selectedArea, assignmentType]);

  useEffect(() => {
    if (selectedTopic) {
      const currentUnits =
        assignmentType === "Training" ? trainingUnits : cfdUnits;
      setAvailableUnits(
        currentUnits[selectedTopic as keyof typeof currentUnits] || []
      );
      setSelectedUnits([]);
    } else {
      setAvailableUnits([]);
    }
  }, [selectedTopic, assignmentType]);

  // Effect to clear Department Groups when no Business Units are selected
  useEffect(() => {
    if (selectedBusinessUnits.length === 0) {
      // Clear Department Groups when no Business Units are selected
      setSelectedDepartmentGroups([]);
    }
  }, [selectedBusinessUnits]);

  // Effects for organizational hierarchy
  useEffect(() => {
    if (selectedDepartmentGroups.length > 0) {
      const allDepartments = selectedDepartmentGroups.flatMap(
        (group) =>
          departmentsByGroup[group as keyof typeof departmentsByGroup] || []
      );
      setAvailableDepartments([...new Set(allDepartments)]);
      setSelectedDepartments([]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableDepartments([]);
    }
  }, [selectedDepartmentGroups]);

  useEffect(() => {
    if (selectedDepartments.length > 0) {
      // Get all divisions for all selected departments
      const allDivisions = selectedDepartments.flatMap(
        (dept) =>
          divisionsByDepartment[dept as keyof typeof divisionsByDepartment] ||
          []
      );
      setAvailableDivisions([...new Set(allDivisions)]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableDivisions([]);
    }
  }, [selectedDepartments]);

  useEffect(() => {
    if (selectedDivisions.length > 0) {
      // Get all sub-divisions for all selected divisions
      const allSubDivisions = selectedDivisions.flatMap(
        (div) =>
          subDivisionsByDivision[div as keyof typeof subDivisionsByDivision] ||
          []
      );
      setAvailableSubDivisions([...new Set(allSubDivisions)]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableSubDivisions([]);
    }
  }, [selectedDivisions]);

  useEffect(() => {
    if (selectedSubDivisions.length > 0) {
      // Get all categories for all selected sub-divisions
      const allCategories = selectedSubDivisions.flatMap(
        (subDiv) =>
          categoriesBySubDivision[
            subDiv as keyof typeof categoriesBySubDivision
          ] || []
      );
      setAvailableCategories([...new Set(allCategories)]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableCategories([]);
    }
  }, [selectedSubDivisions]);

  useEffect(() => {
    if (selectedCategories.length > 0) {
      // Get all grades for all selected categories
      const allGrades = selectedCategories.flatMap(
        (cat) => gradesByCategory[cat as keyof typeof gradesByCategory] || []
      );
      setAvailableGrades([...new Set(allGrades)]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
    } else {
      setAvailableGrades([]);
    }
  }, [selectedCategories]);

  useEffect(() => {
    if (selectedGrades.length > 0) {
      // Get all designations for all selected grades
      const allDesignations = selectedGrades.flatMap(
        (grade) =>
          designationsByGrade[grade as keyof typeof designationsByGrade] || []
      );
      setAvailableDesignations([...new Set(allDesignations)]);
      setSelectedDesignations([]);
    } else {
      setAvailableDesignations([]);
    }
  }, [selectedGrades]);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  // Filter assignments based on search term and filters
  const filteredAssignments = trainingAssignments.filter(
    (assignment: TrainingAssignment) => {
      const matchesSearch = assignment.trainingProgram
        .toLowerCase()
        .includes(searchTerm.toLowerCase());

      // Check if the assignment matches each filter
      // If filter array is empty, return true (no filter applied)
      // Otherwise, check if the assignment value is in the filter array
      const matchesBusinessUnit =
        filters.businessUnit.length === 0 ||
        filters.businessUnit.some((filter) =>
          assignment.businessUnit.includes(filter)
        );

      const matchesDepartmentGroup =
        filters.departmentGroup.length === 0 ||
        filters.departmentGroup.some((filter) =>
          assignment.departmentGroup.includes(filter)
        );

      const matchesDepartment =
        filters.department.length === 0 ||
        filters.department.includes(assignment.department);

      const matchesDivision =
        filters.division.length === 0 ||
        filters.division.includes(assignment.division);

      const matchesSubDivision =
        filters.subDivision.length === 0 ||
        filters.subDivision.includes(assignment.subDivision);

      const matchesCategory =
        filters.category.length === 0 ||
        filters.category.includes(assignment.category);

      const matchesGrade =
        filters.grade.length === 0 || filters.grade.includes(assignment.grade);

      const matchesDesignation =
        filters.designation.length === 0 ||
        filters.designation.includes(assignment.designation);

      const matchesAssignmentStatus =
        filters.assignmentStatus.length === 0 ||
        filters.assignmentStatus.includes(assignment.assignmentStatus);

      return (
        matchesSearch &&
        matchesBusinessUnit &&
        matchesDepartmentGroup &&
        matchesDepartment &&
        matchesDivision &&
        matchesSubDivision &&
        matchesCategory &&
        matchesGrade &&
        matchesDesignation &&
        matchesAssignmentStatus
      );
    }
  );

  // Calculate pagination
  const indexOfLastItem = currentPage * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredAssignments.slice(
    indexOfFirstItem,
    indexOfLastItem
  );
  const totalPages = Math.ceil(filteredAssignments.length / itemsPerPage);

  const goToPage = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters((prev) => {
      // If value is "all", clear the filter
      if (value === "all") {
        return {
          ...prev,
          [key]: [],
        };
      }

      // Check if the value is already in the array
      const currentValues = prev[key as keyof typeof prev] as string[];
      if (currentValues.includes(value)) {
        // Remove the value if it's already selected
        return {
          ...prev,
          [key]: currentValues.filter((v) => v !== value),
        };
      } else {
        // Add the value if it's not already selected
        return {
          ...prev,
          [key]: [...currentValues, value],
        };
      }
    });
    setCurrentPage(1); // Reset to first page when filters change
  };

  // Function to check if a filter value is selected
  const isFilterSelected = (key: string, value: string): boolean => {
    const filterValues = filters[key as keyof typeof filters] as string[];
    return filterValues.includes(value);
  };

  const clearFilters = () => {
    setFilters({
      businessUnit: [],
      departmentGroup: [],
      department: [],
      division: [],
      subDivision: [],
      category: [],
      grade: [],
      designation: [],
      assignmentStatus: [],
    });
    setSearchTerm("");
    setCurrentPage(1);
  };

  // Count total active filters
  const activeFilterCount = Object.values(filters).reduce(
    (count, filterValues) => count + (filterValues as string[]).length,
    0
  );

  // Handler for updating assignment status
  const handleAssignmentStatusChange = (
    assignmentId: string,
    newStatus: "Active" | "Suspended"
  ) => {
    setTrainingAssignments((prevAssignments) =>
      prevAssignments.map((assignment) =>
        assignment.id === assignmentId
          ? { ...assignment, assignmentStatus: newStatus }
          : assignment
      )
    );

    toast({
      title: "Assignment Status Updated",
      description: `Assignment status has been changed to ${newStatus}.`,
    });
  };

  const handleAssignTraining = () => {
    // Validate required fields
    if (
      (assignmentType === "Training"
        ? selectedTopics.length === 0 && !allUnitsSelected
        : selectedCfdUnits.length === 0 && !allUnitsSelected) ||
      selectedBusinessUnits.length === 0
    ) {
      toast({
        title: "Missing Information",
        description:
          assignmentType === "Training"
            ? "Please select at least one topic and at least one business unit."
            : "Please select at least one document and at least one business unit.",
        variant: "destructive",
      });
      return;
    }

    // Validate organizational hierarchy fields
    if (
      selectedDepartmentGroups.length === 0 ||
      selectedDepartments.length === 0 ||
      selectedDivisions.length === 0 ||
      selectedSubDivisions.length === 0 ||
      selectedCategories.length === 0 ||
      selectedGrades.length === 0 ||
      selectedDesignations.length === 0
    ) {
      toast({
        title: "Missing Information",
        description: "Please complete all organizational hierarchy selections.",
        variant: "destructive",
      });
      return;
    }

    // Validate that at least one employee is selected
    if (selectedEmployeeIds.length === 0) {
      toast({
        title: "Missing Information",
        description: "Please select at least one employee.",
        variant: "destructive",
      });
      return;
    }

    // Create a single training assignment for all selected employees
    const newAssignment = {
      id: `${trainingAssignments.length + 1}`,
      employeeIds: selectedEmployeeIds, // Store all employee IDs
      employeeName:
        selectedEmployeeIds.length === 1
          ? users.find((user) => user.id === selectedEmployeeIds[0])?.name ||
            "Unknown"
          : `${selectedEmployeeIds.length} Employees`, // For display purposes
      // Create a training program string that includes all selected units
      trainingProgram: allUnitsSelected
        ? assignmentType === "Training"
          ? "All Units Selected"
          : "All Documents Selected"
        : assignmentType === "Training"
        ? selectedTopics
            .map(({ area, topic }) => {
              return `${area} -> ${topic}`;
            })
            .join(" | ")
        : selectedCfdUnits
            .map(({ area, topic, unit }) => {
              return `${area} -> ${topic} -> ${unit}`;
            })
            .join(" | "),
      businessUnit: selectedBusinessUnits.join(", "),
      departmentGroup: selectedDepartmentGroups.join(", "),
      department: selectedDepartments.join(", "),
      division: selectedDivisions.join(", "),
      subDivision: selectedSubDivisions.join(", "),
      category: selectedCategories.join(", "),
      grade: selectedGrades.join(", "),
      designation: selectedDesignations.join(", "),
      status: "Not Started",
      assignmentStatus: "Active" as "Active" | "Suspended",
      assignedDate: new Date().toISOString().split("T")[0],
      notes: notes,
    };

    // Add the new assignment to the state
    setTrainingAssignments([...trainingAssignments, newAssignment]);

    // Reset form fields
    setSelectedArea("");
    setSelectedTopic("");
    setSelectedUnits([]);
    setSelectedTopics([]);
    setSelectedCfdUnits([]);
    setAllUnitsSelected(false);
    setSelectedBusinessUnits([]);
    setSelectedDepartmentGroups([]);
    setSelectedDepartments([]);
    setSelectedDivisions([]);
    setSelectedSubDivisions([]);
    setSelectedCategories([]);
    setSelectedGrades([]);
    setSelectedDesignations([]);
    setSelectedEmployeeIds([]);
    setNotes("");

    // Close dialog
    setDialogOpen(false);

    toast({
      title: `${assignmentType} Assigned`,
      description: `${assignmentType} has been assigned to ${selectedEmployeeIds.length} employee(s) successfully.`,
    });
  };

  // Reset form when dialog is closed
  const handleDialogOpenChange = (open: boolean) => {
    if (!open) {
      // Reset form fields when dialog is closed
      setSelectedArea("");
      setSelectedTopic("");
      setSelectedUnits([]);
      setSelectedTopics([]);
      setSelectedCfdUnits([]);
      setAllUnitsSelected(false);
      setSelectedBusinessUnits([]);
      setSelectedDepartmentGroups([]);
      setSelectedDepartments([]);
      setSelectedDivisions([]);
      setSelectedSubDivisions([]);
      setSelectedCategories([]);
      setSelectedGrades([]);
      setSelectedDesignations([]);
      setSelectedEmployeeIds([]);
      setNotes("");
      setProgramSearchTerm(""); // Reset program search term
      setAssignmentType("Training"); // Reset assignment type to default
    }
    setDialogOpen(open);
  };

  return (
    <>
      <motion.div
        className="space-y-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <motion.div variants={itemVariants}>
          <h1 className="text-3xl font-bold tracking-tight mb-2">
            Employee Training & CFD Assignment
          </h1>
          <p className="text-muted-foreground">
            Assign and manage training programs and CFDs for employees
          </p>

          {/* Selected Business Units at the top of the page */}
          {filters.businessUnit.length > 0 && (
            <div className="mt-4">
              <div className="border rounded-md bg-white">
                {/* Header without background */}
                <div className="px-3 py-2">
                  <div className="flex items-center justify-between w-full">
                    <h4 className="text-sm font-medium">
                      Selected Business Units
                      <span className="text-xs text-muted-foreground ml-2">
                        {filters.businessUnit.length} selected
                      </span>
                    </h4>
                  </div>
                </div>
                {/* Content */}
                <div className="px-3 pt-0 pb-3">
                  <div className="flex flex-wrap gap-1 mt-2">
                    {filters.businessUnit.map((unit) => (
                      <SelectedItemTag
                        key={unit}
                        label={unit}
                        onRemove={() =>
                          handleFilterChange("businessUnit", unit)
                        }
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </motion.div>

        <motion.div variants={itemVariants}>
          <Card className="p-4 md:p-6 shadow-md border-muted/40">
            {/* Adjusted layout for header - Two row layout */}
            <div className="flex flex-col space-y-4 mb-6">
              {/* Single Row: Library Tabs and Assignment Buttons */}
              <div className="flex flex-wrap gap-2 justify-between items-center">
                {/* Library Tabs */}
                <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
                  <TabsList className="grid w-full grid-cols-4 max-w-4xl">
                    <TabsTrigger
                      value="training-library"
                      className="gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path><path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path></svg>
                      Training Library & Assignment
                    </TabsTrigger>
                    <TabsTrigger
                      value="doc-library"
                      className="gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>
                      Doc Library & Assignment
                    </TabsTrigger>
                    <TabsTrigger
                      value="forms-library"
                      className="gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>
                      Forms Library & Assignment
                    </TabsTrigger>
                    <TabsTrigger
                      value="checklist-library"
                      className="gap-2"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path><circle cx="12" cy="13" r="3"></circle></svg>
                      Checklist Library & Assignment
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                {/* Assignment Buttons */}
                <div className="flex gap-2">
                  <Button
                    className="gap-2 transition-all duration-200 hover:bg-primary/90 shadow-sm"
                    onClick={() => {
                      setAssignmentType("Training");
                      setDialogOpen(true);
                    }}
                  >
                    <Plus size={16} />
                    Assign Training
                  </Button>
                  <Button
                    className="gap-2 transition-all duration-200 hover:bg-primary/90 shadow-sm"
                    onClick={() => {
                      setAssignmentType("CFD");
                      setDialogOpen(true);
                    }}
                  >
                    <Plus size={16} />
                    Assign CFD
                  </Button>
                </div>
              </div>

              {/* Tab Content */}
              <div className="mt-6">
                <Tabs value={activeTab} onValueChange={setActiveTab}>
                  <TabsContent value="training-library" className="space-y-4">
                    {(() => {
                      const trainingTableData = generateTrainingTableData();

                      // Helper function to get unique values for dropdowns
                      const getTrainingUniqueValues = (field: keyof typeof trainingFilters) => {
                        const values = trainingTableData.map(item => item[field]).filter(Boolean);
                        return [...new Set(values)].sort();
                      };

                      // Helper functions for filter management
                      const handleTrainingFilterChange = (field: string, value: string) => {
                        setTrainingFilters(prev => ({
                          ...prev,
                          [field]: value
                        }));

                        // Clear dropdown selection when using text filter
                        if (value) {
                          setTrainingSelectedValues(prev => ({
                            ...prev,
                            [field]: ""
                          }));
                        }
                      };

                      const handleTrainingDropdownSelect = (field: string, value: string) => {
                        // If "all" is selected, clear the filter
                        if (value === "all") {
                          setTrainingSelectedValues(prev => ({
                            ...prev,
                            [field]: ""
                          }));
                        } else {
                          setTrainingSelectedValues(prev => ({
                            ...prev,
                            [field]: value
                          }));
                        }

                        // Clear the text filter when using dropdown
                        setTrainingFilters(prev => ({
                          ...prev,
                          [field]: ""
                        }));
                      };

                      const clearTrainingFilter = (field: string) => {
                        setTrainingFilters(prev => ({
                          ...prev,
                          [field]: ""
                        }));
                        setTrainingSelectedValues(prev => ({
                          ...prev,
                          [field]: ""
                        }));
                      };

                      const clearAllTrainingFilters = () => {
                        setTrainingFilters({
                          area: "",
                          topic: "",
                          topicId: "",
                          version: "",
                          responsibleDept: "",
                          contentOwner: ""
                        });
                        setTrainingSelectedValues({
                          area: "",
                          topic: "",
                          topicId: "",
                          version: "",
                          responsibleDept: "",
                          contentOwner: ""
                        });
                        setTrainingSelectedArea("");
                        setTrainingSearchTerm("");
                        setTrainingOpenPopover(null);
                      };



                      // Enhanced filtering logic
                      const trainingFilteredData = trainingTableData.filter((item) => {
                        // Global search
                        const matchesSearch = trainingSearchTerm === "" ||
                          item.area.toLowerCase().includes(trainingSearchTerm.toLowerCase()) ||
                          item.topic.toLowerCase().includes(trainingSearchTerm.toLowerCase()) ||
                          item.topicId.toLowerCase().includes(trainingSearchTerm.toLowerCase()) ||
                          item.responsibleDept.toLowerCase().includes(trainingSearchTerm.toLowerCase()) ||
                          item.contentOwner.toLowerCase().includes(trainingSearchTerm.toLowerCase());

                        // Legacy area filter
                        const matchesArea = trainingSelectedArea === "" || item.area === trainingSelectedArea;

                        // Column-specific filters
                        const matchesAreaFilter = (trainingSelectedValues.area || item.area.toLowerCase().includes(trainingFilters.area.toLowerCase())) &&
                                                  (!trainingSelectedValues.area || item.area === trainingSelectedValues.area) &&
                                                  (!trainingFilters.area || item.area.toLowerCase().includes(trainingFilters.area.toLowerCase()));

                        const matchesTopicFilter = (trainingSelectedValues.topic || item.topic.toLowerCase().includes(trainingFilters.topic.toLowerCase())) &&
                                                   (!trainingSelectedValues.topic || item.topic === trainingSelectedValues.topic) &&
                                                   (!trainingFilters.topic || item.topic.toLowerCase().includes(trainingFilters.topic.toLowerCase()));

                        const matchesTopicIdFilter = (trainingSelectedValues.topicId || item.topicId.toLowerCase().includes(trainingFilters.topicId.toLowerCase())) &&
                                                     (!trainingSelectedValues.topicId || item.topicId === trainingSelectedValues.topicId) &&
                                                     (!trainingFilters.topicId || item.topicId.toLowerCase().includes(trainingFilters.topicId.toLowerCase()));

                        const matchesVersionFilter = (trainingSelectedValues.version || item.version.toLowerCase().includes(trainingFilters.version.toLowerCase())) &&
                                                     (!trainingSelectedValues.version || item.version === trainingSelectedValues.version) &&
                                                     (!trainingFilters.version || item.version.toLowerCase().includes(trainingFilters.version.toLowerCase()));

                        const matchesResponsibleDeptFilter = (trainingSelectedValues.responsibleDept || item.responsibleDept.toLowerCase().includes(trainingFilters.responsibleDept.toLowerCase())) &&
                                                             (!trainingSelectedValues.responsibleDept || item.responsibleDept === trainingSelectedValues.responsibleDept) &&
                                                             (!trainingFilters.responsibleDept || item.responsibleDept.toLowerCase().includes(trainingFilters.responsibleDept.toLowerCase()));

                        const matchesContentOwnerFilter = (trainingSelectedValues.contentOwner || item.contentOwner.toLowerCase().includes(trainingFilters.contentOwner.toLowerCase())) &&
                                                          (!trainingSelectedValues.contentOwner || item.contentOwner === trainingSelectedValues.contentOwner) &&
                                                          (!trainingFilters.contentOwner || item.contentOwner.toLowerCase().includes(trainingFilters.contentOwner.toLowerCase()));

                        return matchesSearch && matchesArea &&
                               matchesAreaFilter && matchesTopicFilter && matchesTopicIdFilter &&
                               matchesVersionFilter && matchesResponsibleDeptFilter && matchesContentOwnerFilter;
                      });

                      return (
                        <Card className="shadow-sm border-slate-200">
                          <div className="p-6">
                            {/* Search and Filter Controls */}
                            <div className="flex flex-col sm:flex-row gap-4 mb-6">
                              <div className="relative flex-1">
                                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                <Input
                                  placeholder="Search areas, topics, or IDs..."
                                  value={trainingSearchTerm}
                                  onChange={(e) => setTrainingSearchTerm(e.target.value)}
                                  className="pl-10"
                                />
                              </div>

                              <select
                                value={trainingSelectedArea}
                                onChange={(e) => setTrainingSelectedArea(e.target.value)}
                                className="px-3 py-2 border border-input bg-background rounded-md text-sm"
                              >
                                <option value="">All Areas</option>
                                {trainingAreas.map((area) => (
                                  <option key={area} value={area}>
                                    {area}
                                  </option>
                                ))}
                              </select>

                              <Button
                                variant="outline"
                                onClick={clearAllTrainingFilters}
                                className="gap-2"
                                disabled={!Object.values(trainingFilters).some(v => v !== "") && !Object.values(trainingSelectedValues).some(v => v !== "")}
                              >
                                Clear All Filters
                              </Button>

                              <Button variant="outline" className="gap-2">
                                <Download size={16} />
                                Export
                              </Button>
                            </div>

                            {/* Results Summary */}
                            <div className="mb-4 text-sm text-muted-foreground">
                              Showing {trainingFilteredData.length} of {trainingTableData.length} training items
                              {trainingSearchTerm && ` for "${trainingSearchTerm}"`}
                              {trainingSelectedArea && ` in ${trainingSelectedArea}`}
                            </div>

                            {/* Training Library Table */}
                            <div className="border rounded-lg overflow-hidden">
                              <Table>
                                <TableHeader className="bg-slate-50">
                                  <TableRow>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Area"
                                        fieldKey="area"
                                        uniqueValues={getTrainingUniqueValues('area')}
                                        filterValue={trainingFilters.area}
                                        selectedValue={trainingSelectedValues.area}
                                        isOpen={trainingOpenPopover === 'area'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'area' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Topic"
                                        fieldKey="topic"
                                        uniqueValues={getTrainingUniqueValues('topic')}
                                        filterValue={trainingFilters.topic}
                                        selectedValue={trainingSelectedValues.topic}
                                        isOpen={trainingOpenPopover === 'topic'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'topic' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Topic ID"
                                        fieldKey="topicId"
                                        uniqueValues={getTrainingUniqueValues('topicId')}
                                        filterValue={trainingFilters.topicId}
                                        selectedValue={trainingSelectedValues.topicId}
                                        isOpen={trainingOpenPopover === 'topicId'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'topicId' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Version"
                                        fieldKey="version"
                                        uniqueValues={getTrainingUniqueValues('version')}
                                        filterValue={trainingFilters.version}
                                        selectedValue={trainingSelectedValues.version}
                                        isOpen={trainingOpenPopover === 'version'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'version' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Responsible Dept."
                                        fieldKey="responsibleDept"
                                        uniqueValues={getTrainingUniqueValues('responsibleDept')}
                                        filterValue={trainingFilters.responsibleDept}
                                        selectedValue={trainingSelectedValues.responsibleDept}
                                        isOpen={trainingOpenPopover === 'responsibleDept'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'responsibleDept' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">
                                      <TableColumnFilter
                                        columnName="Content Owner"
                                        fieldKey="contentOwner"
                                        uniqueValues={getTrainingUniqueValues('contentOwner')}
                                        filterValue={trainingFilters.contentOwner}
                                        selectedValue={trainingSelectedValues.contentOwner}
                                        isOpen={trainingOpenPopover === 'contentOwner'}
                                        onOpenChange={(open) => setTrainingOpenPopover(open ? 'contentOwner' : null)}
                                        onFilterChange={handleTrainingFilterChange}
                                        onDropdownSelect={handleTrainingDropdownSelect}
                                        onClearFilter={clearTrainingFilter}
                                      />
                                    </TableHead>
                                    <TableHead className="font-medium">Assigned Employee</TableHead>
                                    <TableHead className="font-medium text-center">Action</TableHead>
                                  </TableRow>
                                </TableHeader>
                                <TableBody>
                                  {trainingFilteredData.length > 0 ? (
                                    trainingFilteredData.map((item, index) => (
                                      <TableRow
                                        key={index}
                                        className="hover:bg-muted/50"
                                      >
                                        <TableCell className="font-medium">{item.area}</TableCell>
                                        <TableCell>
                                          <span className="font-semibold text-primary">{item.topic}</span>
                                        </TableCell>
                                        <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                                        <TableCell>{item.version}</TableCell>
                                        <TableCell>{item.responsibleDept}</TableCell>
                                        <TableCell>{item.contentOwner}</TableCell>
                                        <TableCell>
                                          <div className="text-sm">
                                            {getTrainingAssignedUsersDisplay(item.topicId)}
                                          </div>
                                        </TableCell>
                                        <TableCell>
                                          <div className="flex gap-2 justify-center">
                                            <Button
                                              size="sm"
                                              className="gap-1 bg-purple-600 hover:bg-purple-700"
                                              onClick={() => handleTrainingAssignUsers(item)}
                                            >
                                              <Users size={14} />
                                              Assign/Passage
                                            </Button>
                                            <Button
                                              size="sm"
                                              variant="outline"
                                              className="gap-1"
                                              onClick={() => {
                                                setTrainingSelectedAssignmentItem(item);
                                                setTrainingViewAssignmentOpen(true);
                                              }}
                                            >
                                              <Eye size={14} />
                                              View Assignment
                                            </Button>
                                          </div>
                                        </TableCell>
                                      </TableRow>
                                    ))
                                  ) : (
                                    <TableRow>
                                      <TableCell colSpan={8} className="h-24 text-center">
                                        <div className="flex flex-col items-center justify-center text-muted-foreground">
                                          <Filter className="h-8 w-8 mb-2 opacity-40" />
                                          <p className="text-sm font-medium">No training content found</p>
                                          <p className="text-xs mt-1">
                                            Try adjusting your search or filter criteria
                                          </p>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  )}
                                </TableBody>
                              </Table>
                            </div>
                          </div>
                        </Card>
                      );
                    })()}
                  </TabsContent>

                  <TabsContent value="doc-library" className="space-y-4">
                    {(() => {
                      const documentTableData = generateDocumentTableData();

                      // Helper function to get unique values for dropdowns
                      const getDocumentUniqueValues = (field: keyof typeof docFilters) => {
                        const values = documentTableData.map(item => item[field]).filter(Boolean);
                        return [...new Set(values)].sort();
                      };

                      // Apply filters to document data
                      const filteredDocumentData = documentTableData.filter(item => {
                        return (
                          (!docFilters.area || item.area.toLowerCase().includes(docFilters.area.toLowerCase())) &&
                          (!docFilters.topic || item.topic.toLowerCase().includes(docFilters.topic.toLowerCase())) &&
                          (!docFilters.topicId || item.topicId.toLowerCase().includes(docFilters.topicId.toLowerCase())) &&
                          (!docFilters.version || item.version.toLowerCase().includes(docFilters.version.toLowerCase())) &&
                          (!docFilters.responsibleDept || item.responsibleDept.toLowerCase().includes(docFilters.responsibleDept.toLowerCase())) &&
                          (!docFilters.contentOwner || item.contentOwner.toLowerCase().includes(docFilters.contentOwner.toLowerCase())) &&
                          (!docFilters.unit || item.unit.toLowerCase().includes(docFilters.unit.toLowerCase()))
                        );
                      });

                      return (
                        <div className="space-y-4">
                          {/* Header with search and filters */}
                          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                            <div>
                              <h3 className="text-lg font-semibold">Doc Library & Assignment</h3>
                              <p className="text-sm text-muted-foreground">Browse and assign document content from the library.</p>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setDocFilters({
                                    area: "",
                                    topic: "",
                                    topicId: "",
                                    version: "",
                                    responsibleDept: "",
                                    contentOwner: "",
                                    unit: "",
                                  });
                                }}
                                className="gap-1"
                              >
                                <X size={14} />
                                Clear Filters
                              </Button>
                            </div>
                          </div>

                          {/* Filters Row */}
                          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-2">
                            <TableColumnFilter
                              placeholder="Filter Area"
                              value={docFilters.area}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, area: value }))}
                              options={getDocumentUniqueValues('area')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic"
                              value={docFilters.topic}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, topic: value }))}
                              options={getDocumentUniqueValues('topic')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic ID"
                              value={docFilters.topicId}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, topicId: value }))}
                              options={getDocumentUniqueValues('topicId')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Version"
                              value={docFilters.version}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, version: value }))}
                              options={getDocumentUniqueValues('version')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Responsible Dept"
                              value={docFilters.responsibleDept}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, responsibleDept: value }))}
                              options={getDocumentUniqueValues('responsibleDept')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Content Owner"
                              value={docFilters.contentOwner}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, contentOwner: value }))}
                              options={getDocumentUniqueValues('contentOwner')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Unit"
                              value={docFilters.unit}
                              onChange={(value) => setDocFilters(prev => ({ ...prev, unit: value }))}
                              options={getDocumentUniqueValues('unit')}
                            />
                          </div>

                          {/* Table */}
                          <div className="border rounded-lg overflow-hidden">
                            <Table>
                              <TableHeader className="bg-slate-50">
                                <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                                  <TableHead className="font-medium text-left">Area</TableHead>
                                  <TableHead className="font-medium text-left">Topic</TableHead>
                                  <TableHead className="font-medium text-left">Unit</TableHead>
                                  <TableHead className="font-medium text-left">Topic ID</TableHead>
                                  <TableHead className="font-medium text-left">Version</TableHead>
                                  <TableHead className="font-medium text-left">Responsible Dept</TableHead>
                                  <TableHead className="font-medium text-left">Content Owner</TableHead>
                                  <TableHead className="font-medium text-center">Assigned Users</TableHead>
                                  <TableHead className="font-medium text-center">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {filteredDocumentData.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                      No documents found matching the current filters.
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  filteredDocumentData.map((item, index) => (
                                    <TableRow key={`${item.topicId}-${index}`} className="hover:bg-slate-50/50">
                                      <TableCell className="font-medium">{item.area}</TableCell>
                                      <TableCell>{item.topic}</TableCell>
                                      <TableCell>{item.unit}</TableCell>
                                      <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                                      <TableCell>{item.version}</TableCell>
                                      <TableCell>{item.responsibleDept}</TableCell>
                                      <TableCell>{item.contentOwner}</TableCell>
                                      <TableCell>
                                        <div className="text-sm">
                                          {getDocAssignedUsersDisplay(item.topicId)}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex gap-2 justify-center">
                                          <Button
                                            size="sm"
                                            className="gap-1 bg-purple-600 hover:bg-purple-700"
                                            onClick={() => handleDocAssignUsers(item)}
                                          >
                                            <Users size={14} />
                                            Assign/Passage
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="gap-1"
                                            onClick={() => {
                                              setDocSelectedAssignmentItem(item);
                                              setDocViewAssignmentOpen(true);
                                            }}
                                          >
                                            <Eye size={14} />
                                            View Assignment
                                          </Button>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Results summary */}
                          <div className="text-sm text-muted-foreground">
                            Showing {filteredDocumentData.length} of {documentTableData.length} documents
                          </div>
                        </div>
                      );
                    })()}
                  </TabsContent>

                  <TabsContent value="forms-library" className="space-y-4">
                    {(() => {
                      const formTableData = generateFormTableData();

                      // Helper function to get unique values for dropdowns
                      const getFormUniqueValues = (field: keyof typeof formsFilters) => {
                        const values = formTableData.map(item => item[field]).filter(Boolean);
                        return [...new Set(values)].sort();
                      };

                      // Apply filters to form data
                      const filteredFormData = formTableData.filter(item => {
                        return (
                          (!formsFilters.area || item.area.toLowerCase().includes(formsFilters.area.toLowerCase())) &&
                          (!formsFilters.topic || item.topic.toLowerCase().includes(formsFilters.topic.toLowerCase())) &&
                          (!formsFilters.topicId || item.topicId.toLowerCase().includes(formsFilters.topicId.toLowerCase())) &&
                          (!formsFilters.version || item.version.toLowerCase().includes(formsFilters.version.toLowerCase())) &&
                          (!formsFilters.responsibleDept || item.responsibleDept.toLowerCase().includes(formsFilters.responsibleDept.toLowerCase())) &&
                          (!formsFilters.contentOwner || item.contentOwner.toLowerCase().includes(formsFilters.contentOwner.toLowerCase())) &&
                          (!formsFilters.unit || item.unit.toLowerCase().includes(formsFilters.unit.toLowerCase()))
                        );
                      });

                      return (
                        <div className="space-y-4">
                          {/* Header with search and filters */}
                          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                            <div>
                              <h3 className="text-lg font-semibold">Forms Library & Assignment</h3>
                              <p className="text-sm text-muted-foreground">Browse and assign form content from the library.</p>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setFormsFilters({
                                    area: "",
                                    topic: "",
                                    topicId: "",
                                    version: "",
                                    responsibleDept: "",
                                    contentOwner: "",
                                    unit: "",
                                  });
                                }}
                                className="gap-1"
                              >
                                <X size={14} />
                                Clear Filters
                              </Button>
                            </div>
                          </div>

                          {/* Filters Row */}
                          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-2">
                            <TableColumnFilter
                              placeholder="Filter Area"
                              value={formsFilters.area}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, area: value }))}
                              options={getFormUniqueValues('area')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic"
                              value={formsFilters.topic}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, topic: value }))}
                              options={getFormUniqueValues('topic')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic ID"
                              value={formsFilters.topicId}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, topicId: value }))}
                              options={getFormUniqueValues('topicId')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Version"
                              value={formsFilters.version}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, version: value }))}
                              options={getFormUniqueValues('version')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Responsible Dept"
                              value={formsFilters.responsibleDept}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, responsibleDept: value }))}
                              options={getFormUniqueValues('responsibleDept')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Content Owner"
                              value={formsFilters.contentOwner}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, contentOwner: value }))}
                              options={getFormUniqueValues('contentOwner')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Unit"
                              value={formsFilters.unit}
                              onChange={(value) => setFormsFilters(prev => ({ ...prev, unit: value }))}
                              options={getFormUniqueValues('unit')}
                            />
                          </div>

                          {/* Table */}
                          <div className="border rounded-lg overflow-hidden">
                            <Table>
                              <TableHeader className="bg-slate-50">
                                <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                                  <TableHead className="font-medium text-left">Area</TableHead>
                                  <TableHead className="font-medium text-left">Topic</TableHead>
                                  <TableHead className="font-medium text-left">Unit</TableHead>
                                  <TableHead className="font-medium text-left">Topic ID</TableHead>
                                  <TableHead className="font-medium text-left">Version</TableHead>
                                  <TableHead className="font-medium text-left">Responsible Dept</TableHead>
                                  <TableHead className="font-medium text-left">Content Owner</TableHead>
                                  <TableHead className="font-medium text-center">Assigned Users</TableHead>
                                  <TableHead className="font-medium text-center">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {filteredFormData.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                      No forms found matching the current filters.
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  filteredFormData.map((item, index) => (
                                    <TableRow key={`${item.topicId}-${index}`} className="hover:bg-slate-50/50">
                                      <TableCell className="font-medium">{item.area}</TableCell>
                                      <TableCell>{item.topic}</TableCell>
                                      <TableCell>{item.unit}</TableCell>
                                      <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                                      <TableCell>{item.version}</TableCell>
                                      <TableCell>{item.responsibleDept}</TableCell>
                                      <TableCell>{item.contentOwner}</TableCell>
                                      <TableCell>
                                        <div className="text-sm">
                                          {getFormsAssignedUsersDisplay(item.topicId)}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex gap-2 justify-center">
                                          <Button
                                            size="sm"
                                            className="gap-1 bg-purple-600 hover:bg-purple-700"
                                            onClick={() => handleFormsAssignUsers(item)}
                                          >
                                            <Users size={14} />
                                            Assign/Passage
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="gap-1"
                                            onClick={() => {
                                              setFormsSelectedAssignmentItem(item);
                                              setFormsViewAssignmentOpen(true);
                                            }}
                                          >
                                            <Eye size={14} />
                                            View Assignment
                                          </Button>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Results summary */}
                          <div className="text-sm text-muted-foreground">
                            Showing {filteredFormData.length} of {formTableData.length} forms
                          </div>
                        </div>
                      );
                    })()}
                  </TabsContent>

                  <TabsContent value="checklist-library" className="space-y-4">
                    {(() => {
                      const checklistTableData = generateChecklistTableData();

                      // Helper function to get unique values for dropdowns
                      const getChecklistUniqueValues = (field: keyof typeof checklistFilters) => {
                        const values = checklistTableData.map(item => item[field]).filter(Boolean);
                        return [...new Set(values)].sort();
                      };

                      // Apply filters to checklist data
                      const filteredChecklistData = checklistTableData.filter(item => {
                        return (
                          (!checklistFilters.area || item.area.toLowerCase().includes(checklistFilters.area.toLowerCase())) &&
                          (!checklistFilters.topic || item.topic.toLowerCase().includes(checklistFilters.topic.toLowerCase())) &&
                          (!checklistFilters.topicId || item.topicId.toLowerCase().includes(checklistFilters.topicId.toLowerCase())) &&
                          (!checklistFilters.version || item.version.toLowerCase().includes(checklistFilters.version.toLowerCase())) &&
                          (!checklistFilters.responsibleDept || item.responsibleDept.toLowerCase().includes(checklistFilters.responsibleDept.toLowerCase())) &&
                          (!checklistFilters.contentOwner || item.contentOwner.toLowerCase().includes(checklistFilters.contentOwner.toLowerCase())) &&
                          (!checklistFilters.unit || item.unit.toLowerCase().includes(checklistFilters.unit.toLowerCase()))
                        );
                      });

                      return (
                        <div className="space-y-4">
                          {/* Header with search and filters */}
                          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
                            <div>
                              <h3 className="text-lg font-semibold">Checklist Library & Assignment</h3>
                              <p className="text-sm text-muted-foreground">Browse and assign checklist content from the library.</p>
                            </div>
                            <div className="flex gap-2">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => {
                                  setChecklistFilters({
                                    area: "",
                                    topic: "",
                                    topicId: "",
                                    version: "",
                                    responsibleDept: "",
                                    contentOwner: "",
                                    unit: "",
                                  });
                                }}
                                className="gap-1"
                              >
                                <X size={14} />
                                Clear Filters
                              </Button>
                            </div>
                          </div>

                          {/* Filters Row */}
                          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-7 gap-2">
                            <TableColumnFilter
                              placeholder="Filter Area"
                              value={checklistFilters.area}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, area: value }))}
                              options={getChecklistUniqueValues('area')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic"
                              value={checklistFilters.topic}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, topic: value }))}
                              options={getChecklistUniqueValues('topic')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Topic ID"
                              value={checklistFilters.topicId}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, topicId: value }))}
                              options={getChecklistUniqueValues('topicId')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Version"
                              value={checklistFilters.version}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, version: value }))}
                              options={getChecklistUniqueValues('version')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Responsible Dept"
                              value={checklistFilters.responsibleDept}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, responsibleDept: value }))}
                              options={getChecklistUniqueValues('responsibleDept')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Content Owner"
                              value={checklistFilters.contentOwner}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, contentOwner: value }))}
                              options={getChecklistUniqueValues('contentOwner')}
                            />
                            <TableColumnFilter
                              placeholder="Filter Unit"
                              value={checklistFilters.unit}
                              onChange={(value) => setChecklistFilters(prev => ({ ...prev, unit: value }))}
                              options={getChecklistUniqueValues('unit')}
                            />
                          </div>

                          {/* Table */}
                          <div className="border rounded-lg overflow-hidden">
                            <Table>
                              <TableHeader className="bg-slate-50">
                                <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                                  <TableHead className="font-medium text-left">Area</TableHead>
                                  <TableHead className="font-medium text-left">Topic</TableHead>
                                  <TableHead className="font-medium text-left">Unit</TableHead>
                                  <TableHead className="font-medium text-left">Topic ID</TableHead>
                                  <TableHead className="font-medium text-left">Version</TableHead>
                                  <TableHead className="font-medium text-left">Responsible Dept</TableHead>
                                  <TableHead className="font-medium text-left">Content Owner</TableHead>
                                  <TableHead className="font-medium text-center">Assigned Users</TableHead>
                                  <TableHead className="font-medium text-center">Actions</TableHead>
                                </TableRow>
                              </TableHeader>
                              <TableBody>
                                {filteredChecklistData.length === 0 ? (
                                  <TableRow>
                                    <TableCell colSpan={9} className="text-center py-8 text-muted-foreground">
                                      No checklists found matching the current filters.
                                    </TableCell>
                                  </TableRow>
                                ) : (
                                  filteredChecklistData.map((item, index) => (
                                    <TableRow key={`${item.topicId}-${index}`} className="hover:bg-slate-50/50">
                                      <TableCell className="font-medium">{item.area}</TableCell>
                                      <TableCell>{item.topic}</TableCell>
                                      <TableCell>{item.unit}</TableCell>
                                      <TableCell className="font-mono text-sm">{item.topicId}</TableCell>
                                      <TableCell>{item.version}</TableCell>
                                      <TableCell>{item.responsibleDept}</TableCell>
                                      <TableCell>{item.contentOwner}</TableCell>
                                      <TableCell>
                                        <div className="text-sm">
                                          {getChecklistAssignedUsersDisplay(item.topicId)}
                                        </div>
                                      </TableCell>
                                      <TableCell>
                                        <div className="flex gap-2 justify-center">
                                          <Button
                                            size="sm"
                                            className="gap-1 bg-purple-600 hover:bg-purple-700"
                                            onClick={() => handleChecklistAssignUsers(item)}
                                          >
                                            <Users size={14} />
                                            Assign/Passage
                                          </Button>
                                          <Button
                                            size="sm"
                                            variant="outline"
                                            className="gap-1"
                                            onClick={() => {
                                              setChecklistSelectedAssignmentItem(item);
                                              setChecklistViewAssignmentOpen(true);
                                            }}
                                          >
                                            <Eye size={14} />
                                            View Assignment
                                          </Button>
                                        </div>
                                      </TableCell>
                                    </TableRow>
                                  ))
                                )}
                              </TableBody>
                            </Table>
                          </div>

                          {/* Results summary */}
                          <div className="text-sm text-muted-foreground">
                            Showing {filteredChecklistData.length} of {checklistTableData.length} checklists
                          </div>
                        </div>
                      );
                    })()}
                  </TabsContent>
                </Tabs>
              </div>
            </div>

                <Dialog open={dialogOpen} onOpenChange={handleDialogOpenChange}>
                  <DialogContent className="sm:max-w-[1200px] h-[90vh] p-0 flex flex-col overflow-hidden border-muted/40 shadow-lg">
                    <DialogHeader className="px-6 pt-6 pb-2 bg-muted/10">
                      <DialogTitle className="text-xl font-semibold">
                        Knowledge Assignment
                      </DialogTitle>
                      <DialogDescription className="text-muted-foreground">
                        Assign learning content to user based on their role and
                        responsibilities with in the organization.
                      </DialogDescription>
                    </DialogHeader>

                    <div className="flex flex-1 overflow-hidden">
                      {/* Main Content - Left Side */}
                      <ScrollArea className="flex-1 overflow-auto px-6">
                        <div className="grid gap-3 py-4">
                          {/* Step 1: Training Program or CFD Selection */}
                          {currentStep === 1 && (
                            <div className="grid grid-cols-1 gap-4">
                              <h3 className="text-md font-semibold">
                                {assignmentType === "Training"
                                  ? "Training Program"
                                  : "Checklist, Forms, and Documents"}
                              </h3>

                              <div className="border rounded-md p-4 bg-muted/5">
                                <div className="flex justify-between items-center mb-4">
                                  <div className="flex items-center space-x-2">
                                    <input
                                      type="checkbox"
                                      id="select-all-units"
                                      checked={allUnitsSelected}
                                      onChange={(e) => {
                                        setAllUnitsSelected(e.target.checked);
                                        if (e.target.checked) {
                                          if (assignmentType === "Training") {
                                            // For Training, select all topics
                                            const allTopics: {
                                              area: string;
                                              topic: string;
                                            }[] = [];
                                            Object.entries(
                                              trainingTopics
                                            ).forEach(([area, topics]) => {
                                              topics.forEach((topic) => {
                                                allTopics.push({ area, topic });
                                              });
                                            });
                                            setSelectedTopics(allTopics);

                                            // Also select all units for backward compatibility
                                            setSelectedUnits(getAllUnits());
                                          } else {
                                            // For CFD, select all units with area and topic info
                                            const allCfdUnits: {
                                              area: string;
                                              topic: string;
                                              unit: string;
                                            }[] = [];
                                            Object.entries(cfdTopics).forEach(
                                              ([area, topics]) => {
                                                topics.forEach((topic) => {
                                                  const topicUnits =
                                                    cfdUnits[topic] || [];
                                                  topicUnits.forEach((unit) => {
                                                    allCfdUnits.push({
                                                      area,
                                                      topic,
                                                      unit,
                                                    });
                                                  });
                                                });
                                              }
                                            );
                                            setSelectedCfdUnits(allCfdUnits);

                                            // Also select all units for backward compatibility
                                            setSelectedUnits(getAllUnits());
                                          }
                                        } else {
                                          if (assignmentType === "Training") {
                                            // For Training, clear selected topics
                                            setSelectedTopics([]);
                                          } else {
                                            // For CFD, clear selected units
                                            setSelectedCfdUnits([]);
                                          }
                                          setSelectedUnits([]);
                                        }
                                      }}
                                      className="h-4 w-4 rounded border-gray-300"
                                    />
                                    <Label
                                      htmlFor="select-all-units"
                                      className="text-sm font-medium"
                                    >
                                      {assignmentType === "Training"
                                        ? "Select all topics across all areas"
                                        : "Select all documents across all checklists and forms"}
                                    </Label>
                                  </div>
                                  <div className="flex gap-2">
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        if (assignmentType === "Training") {
                                          // For Training, select all topics
                                          const allTopics: {
                                            area: string;
                                            topic: string;
                                          }[] = [];
                                          Object.entries(
                                            trainingTopics
                                          ).forEach(([area, topics]) => {
                                            topics.forEach((topic) => {
                                              allTopics.push({ area, topic });
                                            });
                                          });
                                          setSelectedTopics(allTopics);

                                          // Also select all units
                                          setSelectedUnits(getAllUnits());
                                        } else {
                                          // For CFD, select all units with area and topic info
                                          const allCfdUnits: {
                                            area: string;
                                            topic: string;
                                            unit: string;
                                          }[] = [];
                                          Object.entries(cfdTopics).forEach(
                                            ([area, topics]) => {
                                              topics.forEach((topic) => {
                                                const topicUnits =
                                                  cfdUnits[topic] || [];
                                                topicUnits.forEach((unit) => {
                                                  allCfdUnits.push({
                                                    area,
                                                    topic,
                                                    unit,
                                                  });
                                                });
                                              });
                                            }
                                          );
                                          setSelectedCfdUnits(allCfdUnits);

                                          // Also select all units for backward compatibility
                                          setSelectedUnits(getAllUnits());
                                        }
                                        setAllUnitsSelected(true);
                                      }}
                                      className="h-7 text-xs"
                                    >
                                      {assignmentType === "Training"
                                        ? "Select All Topics"
                                        : "Select All"}
                                    </Button>
                                    <Button
                                      type="button"
                                      variant="outline"
                                      size="sm"
                                      onClick={() => {
                                        if (assignmentType === "Training") {
                                          // For Training, clear selected topics
                                          setSelectedTopics([]);
                                        } else {
                                          // For CFD, clear selected units
                                          setSelectedCfdUnits([]);
                                        }
                                        // For both, clear selected units
                                        setSelectedUnits([]);
                                        setAllUnitsSelected(false);
                                      }}
                                      disabled={selectedUnits.length === 0}
                                      className="h-7 text-xs"
                                    >
                                      {assignmentType === "Training"
                                        ? "Remove All Topics"
                                        : "Remove All"}
                                    </Button>
                                  </div>
                                </div>

                                {/* Search input for filtering */}
                                <div className="mb-4 relative">
                                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
                                  <Input
                                    placeholder={`Search ${
                                      assignmentType === "Training"
                                        ? "areas, topics, or units"
                                        : "checklists, forms, or documents"
                                    }...`}
                                    value={programSearchTerm}
                                    onChange={(e) =>
                                      setProgramSearchTerm(e.target.value)
                                    }
                                    className="pl-10 w-full"
                                  />
                                  {programSearchTerm && (
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 text-muted-foreground hover:text-foreground"
                                      onClick={() => setProgramSearchTerm("")}
                                    >
                                      <X size={16} />
                                    </Button>
                                  )}
                                </div>

                                {/* Tree-like selection UI */}
                                <div className="border rounded-md p-3 bg-background max-h-[300px] overflow-y-auto">
                                  {/* Get filtered data based on search term */}
                                  {(() => {
                                    const {
                                      filteredAreas,
                                      filteredTopics,
                                      filteredUnits,
                                      matchCounts,
                                    } = filterProgramItems(programSearchTerm);

                                    // Display match counts if searching
                                    if (programSearchTerm.trim()) {
                                      const totalMatches =
                                        matchCounts.areas +
                                        matchCounts.topics +
                                        matchCounts.units;
                                      if (totalMatches === 0) {
                                        return (
                                          <div className="text-center py-4 text-muted-foreground">
                                            No matches found for "
                                            {programSearchTerm}"
                                          </div>
                                        );
                                      }
                                    }

                                    return filteredAreas.map((area) => {
                                      // Get all topics for this area
                                      const areaTopics =
                                        filteredTopics[area] || [];

                                      // Check if this area is in the expanded list
                                      const isExpanded =
                                        expandedAreas.includes(area);

                                      return (
                                        <div key={area} className="mb-3">
                                          <Accordion
                                            type="single"
                                            collapsible
                                            value={isExpanded ? area : ""}
                                            onValueChange={(value) => {
                                              if (value === area) {
                                                setExpandedAreas([
                                                  ...expandedAreas,
                                                  area,
                                                ]);
                                              } else {
                                                setExpandedAreas(
                                                  expandedAreas.filter(
                                                    (a) => a !== area
                                                  )
                                                );
                                              }
                                            }}
                                          >
                                            <AccordionItem
                                              value={area}
                                              className="border-0"
                                            >
                                              <AccordionTrigger className="py-1 hover:no-underline">
                                                <div className="flex items-center space-x-2">
                                                  <svg
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    width="16"
                                                    height="16"
                                                    viewBox="0 0 24 24"
                                                    fill="none"
                                                    stroke="currentColor"
                                                    strokeWidth="2"
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    className="text-primary/70"
                                                  >
                                                    <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z"></path>
                                                    <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4"></path>
                                                  </svg>
                                                  <span
                                                    className={`text-sm font-medium ${
                                                      programSearchTerm &&
                                                      area
                                                        .toLowerCase()
                                                        .includes(
                                                          programSearchTerm.toLowerCase()
                                                        )
                                                        ? "bg-yellow-100 px-1 rounded"
                                                        : ""
                                                    }`}
                                                  >
                                                    {area}
                                                  </span>
                                                </div>
                                              </AccordionTrigger>
                                              <AccordionContent className="pt-1 pb-0">
                                                {/* Topics under this area (or Forms under Checklist for CFD) */}
                                                <div className="ml-6 space-y-2">
                                                  {areaTopics.map((topic) => {
                                                    // Get all units for this topic from filtered units
                                                    const topicUnits =
                                                      filteredUnits[topic] ||
                                                      [];

                                                    return (
                                                      <div
                                                        key={topic}
                                                        className="mb-2"
                                                      >
                                                        <div className="flex items-center space-x-2 mb-1">
                                                          <svg
                                                            xmlns="http://www.w3.org/2000/svg"
                                                            width="14"
                                                            height="14"
                                                            viewBox="0 0 24 24"
                                                            fill="none"
                                                            stroke="currentColor"
                                                            strokeWidth="2"
                                                            strokeLinecap="round"
                                                            strokeLinejoin="round"
                                                            className="text-muted-foreground"
                                                          >
                                                            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                                                            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                                                          </svg>
                                                          {assignmentType ===
                                                          "Training" ? (
                                                            <div className="flex items-center space-x-2">
                                                              <input
                                                                type="checkbox"
                                                                id={`topic-${topic}`}
                                                                checked={selectedTopics.some(
                                                                  (t) =>
                                                                    t.area ===
                                                                      area &&
                                                                    t.topic ===
                                                                      topic
                                                                )}
                                                                onChange={(
                                                                  e
                                                                ) => {
                                                                  if (
                                                                    e.target
                                                                      .checked
                                                                  ) {
                                                                    // Add this topic to selected topics
                                                                    setSelectedTopics(
                                                                      [
                                                                        ...selectedTopics,
                                                                        {
                                                                          area,
                                                                          topic,
                                                                        },
                                                                      ]
                                                                    );

                                                                    // Add all units from this topic to selectedUnits
                                                                    const unitsToAdd =
                                                                      topicUnits.filter(
                                                                        (
                                                                          unit
                                                                        ) =>
                                                                          !selectedUnits.includes(
                                                                            unit
                                                                          )
                                                                      );
                                                                    setSelectedUnits(
                                                                      [
                                                                        ...selectedUnits,
                                                                        ...unitsToAdd,
                                                                      ]
                                                                    );
                                                                  } else {
                                                                    // Remove this topic from selected topics
                                                                    setSelectedTopics(
                                                                      selectedTopics.filter(
                                                                        (t) =>
                                                                          !(
                                                                            t.area ===
                                                                              area &&
                                                                            t.topic ===
                                                                              topic
                                                                          )
                                                                      )
                                                                    );

                                                                    // Remove all units from this topic
                                                                    setSelectedUnits(
                                                                      selectedUnits.filter(
                                                                        (
                                                                          unit
                                                                        ) =>
                                                                          !topicUnits.includes(
                                                                            unit
                                                                          )
                                                                      )
                                                                    );
                                                                  }
                                                                }}
                                                                className="h-4 w-4 rounded border-gray-300"
                                                              />
                                                              <span
                                                                className={`text-sm font-medium ${
                                                                  programSearchTerm &&
                                                                  topic
                                                                    .toLowerCase()
                                                                    .includes(
                                                                      programSearchTerm.toLowerCase()
                                                                    )
                                                                    ? "bg-yellow-100 px-1 rounded"
                                                                    : ""
                                                                }`}
                                                              >
                                                                {topic}
                                                              </span>
                                                            </div>
                                                          ) : (
                                                            <span
                                                              className={`text-sm ${
                                                                programSearchTerm &&
                                                                topic
                                                                  .toLowerCase()
                                                                  .includes(
                                                                    programSearchTerm.toLowerCase()
                                                                  )
                                                                  ? "bg-yellow-100 px-1 rounded"
                                                                  : ""
                                                              }`}
                                                            >
                                                              {topic}
                                                            </span>
                                                          )}
                                                        </div>

                                                        {/* Units under this topic (or Documents under Form for CFD) */}
                                                        <div className="ml-6 space-y-1">
                                                          {assignmentType ===
                                                          "Training"
                                                            ? // For Training, just show the units without checkboxes
                                                              topicUnits.map(
                                                                (unit) => (
                                                                  <div
                                                                    key={unit}
                                                                    className="flex items-center space-x-2 pl-6"
                                                                  >
                                                                    <Label
                                                                      className={`text-sm text-muted-foreground ${
                                                                        programSearchTerm &&
                                                                        unit
                                                                          .toLowerCase()
                                                                          .includes(
                                                                            programSearchTerm.toLowerCase()
                                                                          )
                                                                          ? "bg-yellow-100 px-1 rounded"
                                                                          : ""
                                                                      }`}
                                                                    >
                                                                      {unit}
                                                                    </Label>
                                                                  </div>
                                                                )
                                                              )
                                                            : // For CFD, show checkboxes for each unit
                                                              topicUnits.map(
                                                                (unit) => (
                                                                  <div
                                                                    key={unit}
                                                                    className="flex items-center space-x-2"
                                                                  >
                                                                    <input
                                                                      type="checkbox"
                                                                      id={`unit-${unit}`}
                                                                      checked={selectedUnits.includes(
                                                                        unit
                                                                      )}
                                                                      onChange={(
                                                                        e
                                                                      ) => {
                                                                        if (
                                                                          e
                                                                            .target
                                                                            .checked
                                                                        ) {
                                                                          // Add to selectedUnits for backward compatibility
                                                                          setSelectedUnits(
                                                                            [
                                                                              ...selectedUnits,
                                                                              unit,
                                                                            ]
                                                                          );

                                                                          // Add to selectedCfdUnits with area and topic info
                                                                          setSelectedCfdUnits(
                                                                            [
                                                                              ...selectedCfdUnits,
                                                                              {
                                                                                area,
                                                                                topic,
                                                                                unit,
                                                                              },
                                                                            ]
                                                                          );
                                                                        } else {
                                                                          // Remove from selectedUnits
                                                                          setSelectedUnits(
                                                                            selectedUnits.filter(
                                                                              (
                                                                                u
                                                                              ) =>
                                                                                u !==
                                                                                unit
                                                                            )
                                                                          );

                                                                          // Remove from selectedCfdUnits
                                                                          setSelectedCfdUnits(
                                                                            selectedCfdUnits.filter(
                                                                              (
                                                                                u
                                                                              ) =>
                                                                                !(
                                                                                  u.area ===
                                                                                    area &&
                                                                                  u.topic ===
                                                                                    topic &&
                                                                                  u.unit ===
                                                                                    unit
                                                                                )
                                                                            )
                                                                          );
                                                                        }
                                                                      }}
                                                                      className="h-4 w-4 rounded border-gray-300"
                                                                    />
                                                                    <Label
                                                                      htmlFor={`unit-${unit}`}
                                                                      className={`text-sm text-muted-foreground ${
                                                                        programSearchTerm &&
                                                                        unit
                                                                          .toLowerCase()
                                                                          .includes(
                                                                            programSearchTerm.toLowerCase()
                                                                          )
                                                                          ? "bg-yellow-100 px-1 rounded"
                                                                          : ""
                                                                      }`}
                                                                    >
                                                                      {unit}
                                                                    </Label>
                                                                  </div>
                                                                )
                                                              )}
                                                        </div>
                                                      </div>
                                                    );
                                                  })}
                                                </div>
                                              </AccordionContent>
                                            </AccordionItem>
                                          </Accordion>
                                        </div>
                                      );
                                    });
                                  })()}
                                </div>
                              </div>
                            </div>
                          )}

                          {/* Business Unit Multi-select */}
                          <div className="grid grid-cols-1 gap-2">
                            <div>
                              <MultiSelectCheckbox
                                title="Business Units"
                                options={businessUnits}
                                selectedValues={selectedBusinessUnits}
                                onChange={setSelectedBusinessUnits}
                                maxHeight="200px"
                                className="bg-white"
                              />
                            </div>
                          </div>

                          {/* Organizational Hierarchy */}
                          <div className="grid grid-cols-1 gap-2">
                            {/* Department Groups Multi-select - Only shown when at least one Business Unit is selected */}
                            {selectedBusinessUnits.length > 0 ? (
                              <div>
                                <MultiSelectCheckbox
                                  title="Department Groups"
                                  options={departmentGroups}
                                  selectedValues={selectedDepartmentGroups}
                                  onChange={setSelectedDepartmentGroups}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground italic p-2 border rounded-md bg-white">
                                Please select at least one Business Unit to
                                display Department Groups
                              </div>
                            )}

                            {/* Dependent Dropdowns - Displayed one by one */}
                            <div className="grid grid-cols-1 gap-4">
                              <div>
                                <MultiSelectCheckbox
                                  title="Department"
                                  options={availableDepartments}
                                  selectedValues={selectedDepartments}
                                  onChange={setSelectedDepartments}
                                  disabled={availableDepartments.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>

                              <div>
                                <MultiSelectCheckbox
                                  title="Division"
                                  options={availableDivisions}
                                  selectedValues={selectedDivisions}
                                  onChange={setSelectedDivisions}
                                  disabled={availableDivisions.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>

                              <div>
                                <MultiSelectCheckbox
                                  title="Sub-Division"
                                  options={availableSubDivisions}
                                  selectedValues={selectedSubDivisions}
                                  onChange={setSelectedSubDivisions}
                                  disabled={availableSubDivisions.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>

                              <div>
                                <MultiSelectCheckbox
                                  title="Category"
                                  options={availableCategories}
                                  selectedValues={selectedCategories}
                                  onChange={setSelectedCategories}
                                  disabled={availableCategories.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>

                              <div>
                                <MultiSelectCheckbox
                                  title="Grade"
                                  options={availableGrades}
                                  selectedValues={selectedGrades}
                                  onChange={setSelectedGrades}
                                  disabled={availableGrades.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>

                              <div>
                                <MultiSelectCheckbox
                                  title="Designation"
                                  options={availableDesignations}
                                  selectedValues={selectedDesignations}
                                  onChange={setSelectedDesignations}
                                  disabled={availableDesignations.length === 0}
                                  maxHeight="200px"
                                  className="bg-white"
                                />
                              </div>
                            </div>
                          </div>

                          {/* Employee Selection and Notes */}
                          <div className="grid grid-cols-1 gap-4 mt-2">
                            <div>
                              <h3 className="text-md font-semibold">
                                Employee Selection and Notes
                              </h3>
                              <div className="grid grid-cols-1 gap-4 mt-2">
                                <div>
                                  <Accordion
                                    type="single"
                                    collapsible
                                    defaultValue="employee-selection"
                                    className="border rounded-md bg-white"
                                  >
                                    <AccordionItem
                                      value="employee-selection"
                                      className="border-0"
                                    >
                                      <AccordionTrigger
                                        className="px-3 py-2 hover:no-underline hover:bg-gray-100 focus:bg-gray-100"
                                        style={{ backgroundColor: "#f9fafb" }}
                                      >
                                        <div className="flex items-center justify-between w-full">
                                          <h4 className="text-sm font-medium">
                                            Employee Selection
                                          </h4>
                                        </div>
                                      </AccordionTrigger>
                                      <AccordionContent className="px-3 pt-0 pb-3">
                                        <div className="relative">
                                          <Select
                                            onValueChange={(value) => {
                                              // If "all" is selected, select all employees
                                              if (value === "all") {
                                                setSelectedEmployeeIds(
                                                  users.map((user) => user.id)
                                                );
                                              } else if (value === "clear") {
                                                // If "clear" is selected, clear all selections
                                                setSelectedEmployeeIds([]);
                                              } else {
                                                // Otherwise, toggle the selected employee
                                                if (
                                                  selectedEmployeeIds.includes(
                                                    value
                                                  )
                                                ) {
                                                  setSelectedEmployeeIds(
                                                    selectedEmployeeIds.filter(
                                                      (id) => id !== value
                                                    )
                                                  );
                                                } else {
                                                  setSelectedEmployeeIds([
                                                    ...selectedEmployeeIds,
                                                    value,
                                                  ]);
                                                }
                                              }
                                            }}
                                          >
                                            <SelectTrigger className="w-full">
                                              <SelectValue
                                                placeholder={
                                                  selectedEmployeeIds.length ===
                                                  0
                                                    ? "Select employees..."
                                                    : `${selectedEmployeeIds.length} employee(s) selected`
                                                }
                                              />
                                            </SelectTrigger>
                                            <SelectContent>
                                              <SelectItem
                                                value="all"
                                                className="font-medium text-primary"
                                              >
                                                Select all employees
                                              </SelectItem>
                                              {selectedEmployeeIds.length >
                                                0 && (
                                                <SelectItem
                                                  value="clear"
                                                  className="font-medium text-destructive"
                                                >
                                                  Clear selection
                                                </SelectItem>
                                              )}
                                              <div className="h-px bg-muted my-1"></div>
                                              {users.map((user) => (
                                                <SelectItem
                                                  key={user.id}
                                                  value={user.id}
                                                  className={
                                                    selectedEmployeeIds.includes(
                                                      user.id
                                                    )
                                                      ? "bg-primary/10"
                                                      : ""
                                                  }
                                                >
                                                  <div className="flex items-center gap-2">
                                                    <div
                                                      className={
                                                        selectedEmployeeIds.includes(
                                                          user.id
                                                        )
                                                          ? "text-primary"
                                                          : ""
                                                      }
                                                    >
                                                      {user.name} (
                                                      {user.employeeNo}) -{" "}
                                                      {user.department}
                                                    </div>
                                                    {selectedEmployeeIds.includes(
                                                      user.id
                                                    ) && (
                                                      <div className="ml-auto">
                                                        <Check className="h-4 w-4 text-primary" />
                                                      </div>
                                                    )}
                                                  </div>
                                                </SelectItem>
                                              ))}
                                            </SelectContent>
                                          </Select>
                                        </div>
                                      </AccordionContent>
                                    </AccordionItem>
                                  </Accordion>

                                  {/* Display selected employees */}
                                  {selectedEmployeeIds.length > 0 && (
                                    <div className="border rounded-md mt-2 bg-white">
                                      {/* Header without background */}
                                      <div className="px-3 py-2">
                                        <div className="flex items-center justify-between w-full">
                                          <h4 className="text-sm font-medium">
                                            Selected Employees
                                            <span className="text-xs text-muted-foreground ml-2">
                                              {selectedEmployeeIds.length}{" "}
                                              selected
                                            </span>
                                          </h4>
                                        </div>
                                      </div>
                                      {/* Content */}
                                      <div className="px-3 pt-0 pb-3">
                                        <div className="flex flex-wrap gap-1 mt-2">
                                          {selectedEmployees.map((employee) => (
                                            <SelectedItemTag
                                              key={employee.id}
                                              label={`${employee.name} (${employee.employeeNo})`}
                                              onRemove={() =>
                                                setSelectedEmployeeIds(
                                                  selectedEmployeeIds.filter(
                                                    (id) => id !== employee.id
                                                  )
                                                )
                                              }
                                            />
                                          ))}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                                <div>
                                  <Accordion
                                    type="single"
                                    collapsible
                                    defaultValue="notes-input"
                                    className="border rounded-md bg-white"
                                  >
                                    <AccordionItem
                                      value="notes-input"
                                      className="border-0"
                                    >
                                      <AccordionTrigger className="px-3 py-2 hover:no-underline">
                                        <div className="flex items-center justify-between w-full">
                                          <h4 className="text-sm font-medium">
                                            Notes{" "}
                                            <span className="text-xs text-muted-foreground">
                                              (Optional)
                                            </span>
                                          </h4>
                                        </div>
                                      </AccordionTrigger>
                                      <AccordionContent className="px-3 pt-0 pb-3">
                                        <textarea
                                          id="notes"
                                          placeholder="Add any additional notes"
                                          value={notes}
                                          onChange={(e) =>
                                            setNotes(e.target.value)
                                          }
                                          className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                                        />
                                      </AccordionContent>
                                    </AccordionItem>
                                  </Accordion>

                                  {/* Display notes if entered */}
                                  {notes && (
                                    <div className="border rounded-md bg-white mt-2">
                                      {/* Header with neutral background */}
                                      <div
                                        className="px-3 py-2"
                                        style={{ backgroundColor: "#f9fafb" }}
                                      >
                                        <div className="flex items-center justify-between w-full">
                                          <h4 className="text-sm font-medium">
                                            Notes Content
                                          </h4>
                                        </div>
                                      </div>
                                      {/* Content */}
                                      <div className="px-3 pt-0 pb-3">
                                        <div className="text-sm mt-2">
                                          {notes}
                                        </div>
                                      </div>
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </ScrollArea>

                      {/* Selected Topics Section - Right Side */}
                      <div className="w-1/3 border-l border-muted/20 overflow-hidden flex flex-col">
                        <div className="p-4 border-b border-muted/20">
                          <h4 className="text-sm font-semibold flex items-center gap-2">
                            <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                            {assignmentType === "Training"
                              ? "Selected Topics"
                              : "Selected Documents"}
                            <span className="text-xs text-muted-foreground ml-2">
                              {assignmentType === "Training"
                                ? `${selectedTopics.length} selected`
                                : `${selectedCfdUnits.length} selected`}
                            </span>
                          </h4>
                        </div>
                        <ScrollArea className="flex-1 overflow-auto">
                          <div className="p-4">
                            {allUnitsSelected ? (
                              <div className="border rounded-md p-4 bg-white">
                                <div className="text-center font-medium">
                                  {assignmentType === "Training"
                                    ? "All topics across all areas selected"
                                    : "All documents across all checklists and forms selected"}
                                </div>
                              </div>
                            ) : (
                                assignmentType === "Training"
                                  ? selectedTopics.length > 0
                                  : selectedCfdUnits.length > 0
                              ) ? (
                              <div className="border rounded-md overflow-hidden">
                                <div className="p-4 bg-white">
                                  {assignmentType === "Training" ? (
                                    <SelectedUnitsWithHierarchy
                                      selectedUnits={selectedUnits}
                                      areas={trainingAreas}
                                      topics={trainingTopics}
                                      units={trainingUnits}
                                      assignmentType="Training"
                                      selectedTopics={selectedTopics}
                                    />
                                  ) : (
                                    <div className="text-center py-8 text-muted-foreground">
                                      CFD Library Display component removed
                                    </div>
                                  )}
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground italic p-2">
                                {assignmentType === "Training"
                                  ? "No topics selected"
                                  : "No documents selected"}
                              </div>
                            )}
                          </div>
                        </ScrollArea>
                      </div>
                    </div>
                    <DialogFooter className="px-6 py-4 border-t">
                      <Button
                        variant="outline"
                        onClick={() => setDialogOpen(false)}
                      >
                        Cancel
                      </Button>
                      <Button onClick={handleAssignTraining}>Assign </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

            {/* Active Filters Display */}
            {activeFilterCount > 0 && (
              <div className="mt-4">
                <div className="border rounded-md bg-white">
                  {/* Header without background */}
                  <div className="px-3 py-2">
                    <div className="flex items-center justify-between w-full">
                      <h4 className="text-sm font-medium">
                        Active Filters
                        <span className="text-xs text-muted-foreground ml-2">
                          {activeFilterCount} active
                        </span>
                      </h4>
                    </div>
                  </div>
                  {/* Content */}
                  <div className="px-3 pt-0 pb-3">
                    <div className="flex flex-wrap gap-1 mt-2">
                      {Object.entries(filters).map(([key, values]) => {
                        if (Array.isArray(values) && values.length > 0) {
                          return values.map((value) => (
                            <SelectedItemTag
                              key={`${key}-${value}`}
                              label={`${key}: ${value}`}
                              onRemove={() => handleFilterChange(key, value)}
                            />
                          ));
                        }
                        return null;
                      })}
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Summary Panel */}
            {(activeFilterCount > 0 || searchTerm) && (
              <div className="mb-6">
                <Card className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-sm font-semibold text-blue-900">
                      Applied Filters Summary
                    </h3>
                    <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                      {activeFilterCount} filter
                      {activeFilterCount !== 1 ? "s" : ""} active
                    </span>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm">
                    {searchTerm && (
                      <div>
                        <span className="text-blue-700 font-medium">
                          Search:
                        </span>
                        <span className="ml-2 bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                          {searchTerm}
                        </span>
                      </div>
                    )}
                    {filters.businessUnit.length > 0 && (
                      <div>
                        <span className="text-blue-700 font-medium">
                          Business Units:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {filters.businessUnit.map((unit) => (
                            <span
                              key={unit}
                              className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs"
                            >
                              {unit}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {filters.departmentGroup.length > 0 && (
                      <div>
                        <span className="text-blue-700 font-medium">
                          Services:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {filters.departmentGroup.map((dept) => (
                            <span
                              key={dept}
                              className="bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs"
                            >
                              {dept}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {filters.designation.length > 0 && (
                      <div>
                        <span className="text-blue-700 font-medium">
                          Roles:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {filters.designation.map((role) => (
                            <span
                              key={role}
                              className="bg-pink-100 text-pink-800 px-2 py-1 rounded text-xs"
                            >
                              {role}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                    {filters.assignmentStatus.length > 0 && (
                      <div>
                        <span className="text-blue-700 font-medium">
                          Status:
                        </span>
                        <div className="flex flex-wrap gap-1 mt-1">
                          {filters.assignmentStatus.map((status) => (
                            <span
                              key={status}
                              className={`px-2 py-1 rounded text-xs ${
                                status === "Active"
                                  ? "bg-green-100 text-green-800"
                                  : "bg-red-100 text-red-800"
                              }`}
                            >
                              {status}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </Card>
              </div>
            )}

      {/* Training Program Details Dialog */}
      <Dialog open={programDetailsOpen} onOpenChange={setProgramDetailsOpen}>
        <DialogContent className="sm:max-w-[900px] border-muted/40 shadow-lg max-h-[90vh] flex flex-col">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Training Program Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              View detailed information about the selected training program.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 overflow-y-auto pr-2">
            <div className="space-y-6">
              {/* Program Overview */}
              <div>
                <h3 className="text-lg font-semibold mb-3 text-primary/90 flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-primary/70"
                  >
                    <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
                  </svg>
                  Program Overview
                </h3>
                <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <span className="text-xs text-muted-foreground block">
                        Program Name
                      </span>
                      <span className="text-sm font-medium">
                        {selectedProgram || "Training Program"}
                      </span>
                    </div>
                    <div>
                      <span className="text-xs text-muted-foreground block">
                        Type
                      </span>
                      <span className="text-sm font-medium">
                        {selectedProgram === "All Documents Selected"
                          ? "Checklist, Forms, and Documents"
                          : "Training Program"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Training Content */}
              <div>
                <h3 className="text-lg font-semibold mb-3 text-primary/90 flex items-center gap-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-primary/70"
                  >
                    <path d="M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z"></path>
                    <circle cx="12" cy="13" r="3"></circle>
                  </svg>
                  Training Content
                </h3>
                <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm">
                  {selectedProgram === "All Units Selected" ||
                  selectedProgram === "All Documents Selected" ? (
                    <div className="text-center py-8">
                      <div className="text-muted-foreground mb-4">
                        {selectedProgram === "All Units Selected"
                          ? "All training units across all areas and topics have been selected."
                          : "All documents across all checklists and forms have been selected."}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        This includes comprehensive coverage of all available
                        training materials.
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8 text-muted-foreground">
                      Detailed training content information would be displayed here.
                    </div>
                  )}
                </div>
              </div>

              {/* Assignment Information */}
              {(() => {
                // Find assignments that match the selected program
                const matchingAssignments = trainingAssignments.filter(
                  (assignment) => assignment.trainingProgram === selectedProgram
                );

                if (matchingAssignments.length > 0) {
                  return (
                    <div>
                      <h3 className="text-lg font-semibold mb-3 text-primary/90 flex items-center gap-2">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="text-primary/70"
                        >
                          <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                          <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        Assignment Information
                      </h3>
                      <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm">
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div>
                            <span className="text-xs text-muted-foreground block">
                              Total Assignments
                            </span>
                            <span className="text-lg font-semibold text-primary">
                              {matchingAssignments.length}
                            </span>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground block">
                              Assigned Date
                            </span>
                            <span className="text-sm font-medium">
                              {matchingAssignments[0].assignedDate}
                            </span>
                          </div>
                          <div>
                            <span className="text-xs text-muted-foreground block">
                              Status
                            </span>
                            <div
                              className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                                matchingAssignments[0].status === "Completed"
                                  ? "bg-green-100 text-green-800"
                                  : matchingAssignments[0].status === "In Progress"
                                  ? "bg-blue-100 text-blue-800"
                                  : "bg-yellow-100 text-yellow-800"
                              }`}
                            >
                              {matchingAssignments[0].status}
                            </div>
                          </div>
                        </div>

                        {matchingAssignments[0].notes && (
                          <div>
                            <span className="text-xs text-muted-foreground block mb-1">
                              Notes
                            </span>
                            <div className="text-sm bg-muted/20 p-3 rounded border">
                              {matchingAssignments[0].notes}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                }

                return null;
              })()}
            </div>
          </div>

          <DialogFooter className="border-t border-muted/20 pt-4 mt-auto">
            <DialogClose asChild>
              <Button
                type="button"
                variant="secondary"
                className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
              >
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Training Program Details Dialog */}
      <Dialog open={programDetailsOpen} onOpenChange={setProgramDetailsOpen}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] border-muted/40 shadow-lg flex flex-col">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Training Program Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              View the details of the assigned training program.
            </DialogDescription>
          </DialogHeader>

          <div className="py-4 overflow-y-auto pr-2">
            <div className="grid grid-cols-1 gap-4">
              <div>
                <div className="flex items-center gap-2 mb-4">
                  <h3 className="text-md font-semibold text-primary/90">
                    Assigned Training Details
                  </h3>
                  <div className="px-2 py-1 bg-primary/10 rounded-md text-xs font-medium text-primary">
                    {selectedProgram === "All Units Selected" ||
                    selectedProgram === "All Documents Selected"
                      ? selectedProgram === "All Units Selected"
                        ? "Training"
                        : "CFD"
                      : selectedProgram.includes("Checklist")
                      ? "CFD"
                      : "Training"}
                  </div>
                </div>

                {selectedProgram === "All Units Selected" ||
                selectedProgram === "All Documents Selected" ? (
                  <div className="bg-muted/30 p-5 rounded-md border border-muted/40 shadow-sm">
                    <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                      <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                      Selected Training Paths
                    </h4>
                    <div className="max-h-[400px] overflow-y-auto pr-2">
                      {(() => {
                        // For "All Units Selected", create a comprehensive list of all area-topic combinations
                        const isTraining =
                          selectedProgram === "All Units Selected";

                        if (isTraining) {
                          // For Training, create selectedTopics with all area-topic combinations
                          const allSelectedTopics: {
                            area: string;
                            topic: string;
                          }[] = [];

                          trainingAreas.forEach((area: string) => {
                            const areaTopics = trainingTopics[area] || [];
                            areaTopics.forEach((topic: string) => {
                              allSelectedTopics.push({ area, topic });
                            });
                          });

                          return (
                            <SelectedUnitsWithHierarchy
                              selectedUnits={[]}
                              areas={trainingAreas}
                              topics={trainingTopics}
                              units={trainingUnits}
                              assignmentType="Training"
                              selectedTopics={allSelectedTopics}
                            />
                          );
                        } else {
                          // For CFD, create selectedCfdUnits with all area-topic-unit combinations
                          const allSelectedCfdUnits: {
                            area: string;
                            topic: string;
                            unit: string;
                          }[] = [];

                          cfdAreas.forEach((area: string) => {
                            const areaTopics = cfdTopics[area] || [];
                            areaTopics.forEach((topic: string) => {
                              const topicUnits = cfdUnits[topic] || [];
                              topicUnits.forEach((unit: string) => {
                                allSelectedCfdUnits.push({ area, topic, unit });
                              });
                            });
                          });

                          return (
                            <div className="text-center py-8 text-muted-foreground">
                              CFD Library Display component removed
                            </div>
                          );
                        }
                      })()}
                    </div>
                  </div>
                ) : (
                  <div className="bg-muted/30 p-5 rounded-md border border-muted/40 shadow-sm">
                    <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                      <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                      Selected Training Paths
                    </h4>
                    <div className="max-h-[400px] overflow-y-auto pr-2">
                      {(() => {
                        // Extract units from the program string
                        const programParts = selectedProgram
                          .split(" | ")
                          .map((program) => {
                            // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                            return program.split(" -> ");
                          });

                        // Create selectedTopics or selectedCfdUnits based on assignment type
                        if (
                          selectedProgram.includes("Checklist") ||
                          selectedProgram === "All Documents Selected"
                        ) {
                          // For CFD
                          const selectedCfdUnits = programParts.map(
                            (parts) => ({
                              area: parts[0],
                              topic: parts[1],
                              unit: parts[2],
                            })
                          );

                          return (
                            <div className="text-center py-8 text-muted-foreground">
                              CFD Library Display component removed
                            </div>
                          );
                        } else {
                          // For Training
                          const selectedTopics = programParts.map((parts) => ({
                            area: parts[0],
                            topic: parts[1],
                          }));

                          return (
                            <SelectedUnitsWithHierarchy
                              selectedUnits={[]}
                              areas={trainingAreas}
                              topics={trainingTopics}
                              units={trainingUnits}
                              assignmentType="Training"
                              selectedTopics={selectedTopics}
                            />
                          );
                        }
                      })()}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <DialogFooter className="border-t border-muted/20 pt-4 mt-auto">
            <DialogClose asChild>
              <Button
                type="button"
                variant="secondary"
                className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
              >
                Close
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Assignment Details Dialog */}
      <Dialog open={detailsDialogOpen} onOpenChange={setDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[700px] border-muted/40 shadow-lg max-h-[90vh] flex flex-col">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Training Assignment Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              View detailed information about this training assignment.
            </DialogDescription>
          </DialogHeader>

          {selectedAssignment && (
            <div className="py-4 overflow-y-auto pr-2">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Employee Information */}
                <div className="space-y-4">
                  <div>
                    <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-primary/70"
                      >
                        <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                      Employee Information
                    </h3>
                    <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm space-y-3">
                      {/* <div>
                      <span className="text-xs text-muted-foreground block">Name</span>
                      <span className="text-sm font-medium">{selectedAssignment.employeeName}</span>
                    </div> */}
                      <div>
                        <span className="text-xs text-muted-foreground block">
                          Business Unit
                        </span>
                        <span className="text-sm font-medium">
                          {selectedAssignment.businessUnit}
                        </span>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground block">
                          Department Group
                        </span>
                        <span className="text-sm font-medium">
                          {selectedAssignment.departmentGroup}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Department
                          </span>
                          <span className="text-sm font-medium">
                            {selectedAssignment.department}
                          </span>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Division
                          </span>
                          <span className="text-sm font-medium">
                            {selectedAssignment.division}
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Sub-Division
                          </span>
                          <span className="text-sm font-medium">
                            {selectedAssignment.subDivision}
                          </span>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Category
                          </span>
                          <span className="text-sm font-medium">
                            {selectedAssignment.category}
                          </span>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-3">
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Grade
                          </span>
                          <span className="text-sm font-medium">
                            {selectedAssignment.grade}
                          </span>
                        </div>
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Designation
                          </span>
                          <div className="flex flex-wrap gap-1 mt-1">
                            {selectedAssignment.designation
                              .split(", ")
                              .map((designation, index) => (
                                <span
                                  key={index}
                                  className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full"
                                >
                                  {designation}
                                </span>
                              ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Assignment Status */}
                  <div>
                    <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-primary/70"
                      >
                        <path d="M12 22c5.523 0 10-4.477 10-10S17.523 2 12 2 2 6.477 2 12s4.477 10 10 10z"></path>
                        <path d="m9 12 2 2 4-4"></path>
                      </svg>
                      Assignment Status
                    </h3>
                    <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm space-y-3">
                      <div>
                        <span className="text-xs text-muted-foreground block">
                          Status
                        </span>
                        <div
                          className={`inline-flex items-center px-2.5 py-0.5 mt-1 rounded-full text-xs font-medium ${
                            selectedAssignment.status === "Completed"
                              ? "bg-green-100 text-green-800"
                              : selectedAssignment.status === "In Progress"
                              ? "bg-blue-100 text-blue-800"
                              : "bg-yellow-100 text-yellow-800"
                          }`}
                        >
                          {selectedAssignment.status}
                        </div>
                      </div>
                      <div>
                        <span className="text-xs text-muted-foreground block">
                          Assigned Date
                        </span>
                        <span className="text-sm font-medium">
                          {selectedAssignment.assignedDate}
                        </span>
                      </div>
                      {selectedAssignment.notes && (
                        <div>
                          <span className="text-xs text-muted-foreground block">
                            Notes
                          </span>
                          <span className="text-sm">
                            {selectedAssignment.notes}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                {/* Training Program */}
                <div>
                  <h3 className="text-md font-semibold mb-3 text-primary/90 flex items-center gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-primary/70"
                    >
                      <path d="M4 19.5v-15A2.5 2.5 0 0 1 6.5 2H20v20H6.5a2.5 2.5 0 0 1 0-5H20"></path>
                    </svg>
                    {assignmentType === "CFD"
                      ? "Checklist, Forms, and Documents"
                      : "Training Program"}
                  </h3>
                  <div className="bg-muted/30 p-4 rounded-md border border-muted/40 shadow-sm">
                    {selectedAssignment.trainingProgram ===
                      "All Units Selected" ||
                    selectedAssignment.trainingProgram ===
                      "All Documents Selected" ? (
                      <div>
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                          {selectedAssignment.trainingProgram ===
                          "All Units Selected"
                            ? "All Units Selected"
                            : "All Documents Selected"}
                        </h4>
                        <div className="text-sm text-muted-foreground mb-2">
                          {selectedAssignment.trainingProgram ===
                          "All Units Selected"
                            ? "All training units across all areas and topics have been assigned."
                            : "All documents across all checklists and forms have been assigned."}
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2 text-xs gap-1"
                          onClick={() => {
                            setSelectedProgram(
                              selectedAssignment.trainingProgram
                            );
                            setProgramDetailsOpen(true);
                            setDetailsDialogOpen(false);
                          }}
                        >
                          <Eye size={14} />
                          {selectedAssignment.trainingProgram ===
                          "All Units Selected"
                            ? "View All Units"
                            : "View All Documents"}
                        </Button>
                      </div>
                    ) : (
                      <div>
                        <h4 className="font-medium text-sm mb-3 flex items-center gap-2">
                          <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                          Selected Training Paths
                        </h4>
                        <div className="max-h-[300px] overflow-y-auto">
                          {(() => {
                            // Extract units from the program string
                            const programParts =
                              selectedAssignment.trainingProgram
                                .split(" | ")
                                .map((program) => {
                                  // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                                  return program.split(" -> ");
                                });

                            // Create selectedTopics or selectedCfdUnits based on assignment type
                            if (
                              selectedAssignment.trainingProgram.includes(
                                "Checklist"
                              ) ||
                              selectedAssignment.trainingProgram ===
                                "All Documents Selected"
                            ) {
                              // For CFD
                              const selectedCfdUnits = programParts.map(
                                (parts) => ({
                                  area: parts[0],
                                  topic: parts[1],
                                  unit: parts[2],
                                })
                              );

                              return (
                                <div className="text-center py-8 text-muted-foreground">
                                  CFD Library Display component removed
                                </div>
                              );
                            } else {
                              // For Training
                              const selectedTopics = programParts.map(
                                (parts) => ({
                                  area: parts[0],
                                  topic: parts[1],
                                })
                              );

                              return (
                                <SelectedUnitsWithHierarchy
                                  selectedUnits={[]}
                                  areas={trainingAreas}
                                  topics={trainingTopics}
                                  units={trainingUnits}
                                  assignmentType="Training"
                                  selectedTopics={selectedTopics}
                                />
                              );
                            }
                          })()}
                        </div>
                      </div>
                    )}

                    {/* Assigned Users Hierarchical View */}
                    <div className="mt-6 pt-4 border-t border-muted/20">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-sm flex items-center gap-2">
                          <span className="h-2 w-2 rounded-full bg-primary/70"></span>
                          Assigned Users
                        </h4>
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-7 text-xs"
                            >
                              <Users className="h-3.5 w-3.5 mr-1" />
                              View All Users
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="sm:max-w-[600px] border-muted/40 shadow-lg">
                            <DialogHeader className="bg-muted/10 pb-2">
                              <DialogTitle className="text-xl font-semibold">
                                All Users
                              </DialogTitle>
                              <DialogDescription className="text-muted-foreground">
                                Users assigned to this training program.
                              </DialogDescription>
                            </DialogHeader>
                            <div className="py-4">
                              <div className="rounded-lg border overflow-hidden">
                                <Table>
                                  <TableHeader className="bg-muted/30">
                                    <TableRow>
                                      <TableHead>Name</TableHead>
                                      <TableHead>Employee ID</TableHead>
                                      <TableHead>Department</TableHead>
                                      <TableHead>Designation</TableHead>
                                    </TableRow>
                                  </TableHeader>
                                  <TableBody>
                                    <TableRow>
                                      <TableCell className="font-medium">
                                        Alex Johnson
                                      </TableCell>
                                      <TableCell>EMP-001</TableCell>
                                      <TableCell>Engineering</TableCell>
                                      <TableCell>
                                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                          Senior Technician
                                        </span>
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell className="font-medium">
                                        Jamie Smith
                                      </TableCell>
                                      <TableCell>EMP-002</TableCell>
                                      <TableCell>Finance</TableCell>
                                      <TableCell>
                                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                          Department Manager
                                        </span>
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell className="font-medium">
                                        Taylor Brown
                                      </TableCell>
                                      <TableCell>EMP-003</TableCell>
                                      <TableCell>Product</TableCell>
                                      <TableCell>
                                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                          Technical Lead
                                        </span>
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell className="font-medium">
                                        Robin Lee
                                      </TableCell>
                                      <TableCell>EXT-001</TableCell>
                                      <TableCell>Research</TableCell>
                                      <TableCell>
                                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                          Specialist
                                        </span>
                                      </TableCell>
                                    </TableRow>
                                    <TableRow>
                                      <TableCell className="font-medium">
                                        Sam Green
                                      </TableCell>
                                      <TableCell>EXT-002</TableCell>
                                      <TableCell>Training</TableCell>
                                      <TableCell>
                                        <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded-full">
                                          Administrative Coordinator
                                        </span>
                                      </TableCell>
                                    </TableRow>
                                  </TableBody>
                                </Table>
                              </div>
                            </div>
                            <DialogFooter className="border-t border-muted/20 pt-4">
                              <DialogClose asChild>
                                <Button
                                  type="button"
                                  variant="secondary"
                                  className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
                                >
                                  Close
                                </Button>
                              </DialogClose>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>

                      {selectedAssignment ? (
                        <div className="p-4 bg-muted/10 rounded-md border border-muted/20">
                          {selectedAssignment.employeeIds &&
                          selectedAssignment.employeeIds.length > 0 ? (
                            <>
                              <p className="text-sm text-muted-foreground mb-2">
                                {selectedAssignment.employeeIds.length}{" "}
                                employee(s) assigned to this training program:
                              </p>
                              <div className="max-h-[100px] overflow-y-auto">
                                {selectedAssignment.employeeIds.map(
                                  (employeeId) => {
                                    const employee = users.find(
                                      (user) => user.id === employeeId
                                    );
                                    return employee ? (
                                      <div
                                        key={employeeId}
                                        className="text-sm py-1 border-b border-muted/10 last:border-0"
                                      >
                                        {employee.name} ({employee.employeeNo})
                                      </div>
                                    ) : null;
                                  }
                                )}
                              </div>
                            </>
                          ) : selectedAssignment.employeeName ? (
                            <p className="text-sm">
                              Assigned to:{" "}
                              <span className="font-medium">
                                {selectedAssignment.employeeName}
                              </span>
                            </p>
                          ) : (
                            <p className="text-sm text-muted-foreground text-center">
                              No employee information available
                            </p>
                          )}
                        </div>
                      ) : (
                        <div className="p-4 bg-muted/10 rounded-md border border-muted/20 text-center">
                          <p className="text-sm text-muted-foreground">
                            Click "View All Users" to see users assigned to this
                            training program.
                          </p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="border-t border-muted/20 pt-4 mt-auto">
            <div className="flex gap-2 w-full justify-between">
              <div className="flex gap-2 flex-wrap">
                <Button
                  type="button"
                  variant="outline"
                  className="transition-all duration-200 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-200 shadow-sm"
                  onClick={() => {
                    setAssignmentToEdit(selectedAssignment);
                    setEditDialogOpen(true);
                    setDetailsDialogOpen(false);
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M17 3a2.85 2.83 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5Z"></path>
                    <path d="m15 5 4 4"></path>
                  </svg>
                  Edit Assignment
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="transition-all duration-200 hover:bg-green-50 hover:text-green-600 hover:border-green-200 shadow-sm"
                  onClick={() => {
                    if (selectedAssignment) {
                      try {
                        let extractedUnits: string[] = [];
                        let unitHierarchy: {
                          area: string;
                          topic: string;
                          unit: string;
                        }[] = [];

                        if (
                          selectedAssignment.trainingProgram ===
                            "All Units Selected" ||
                          selectedAssignment.trainingProgram ===
                            "All Documents Selected"
                        ) {
                          // If all units/documents are selected, get all units from all areas and topics
                          const currentAreas =
                            selectedAssignment.trainingProgram ===
                            "All Documents Selected"
                              ? cfdAreas
                              : trainingAreas;
                          const currentTopics =
                            selectedAssignment.trainingProgram ===
                            "All Documents Selected"
                              ? cfdTopics
                              : trainingTopics;
                          const currentUnits =
                            selectedAssignment.trainingProgram ===
                            "All Documents Selected"
                              ? cfdUnits
                              : trainingUnits;

                          currentAreas.forEach((area: string) => {
                            const areaTopics =
                              currentTopics[
                                area as keyof typeof currentTopics
                              ] || [];
                            (areaTopics as string[]).forEach(
                              (topic: string) => {
                                const topicUnits =
                                  currentUnits[
                                    topic as keyof typeof currentUnits
                                  ] || [];
                                (topicUnits as string[]).forEach(
                                  (unit: string) => {
                                    extractedUnits.push(unit);
                                    unitHierarchy.push({
                                      area,
                                      topic,
                                      unit,
                                    });
                                  }
                                );
                              }
                            );
                          });
                        } else {
                          // Extract units from the program string
                          const programParts =
                            selectedAssignment.trainingProgram
                              .split(" | ")
                              .map((program) => {
                                // Both Training and CFD formats now use the same format: "Area -> Topic -> Unit"
                                return program.split(" -> ");
                              });
                          extractedUnits = programParts.map(
                            (parts) => parts[2]
                          );

                          // Create unit hierarchy data
                          unitHierarchy = programParts.map((parts) => ({
                            area: parts[0],
                            topic: parts[1],
                            unit: parts[2],
                          }));
                        }

                        // Generate and download PDF
                        generateTrainingAssignmentPDF(
                          selectedAssignment,
                          users, // Using the mock users data
                          extractedUnits,
                          unitHierarchy
                        );

                        toast({
                          title: "PDF Generated",
                          description:
                            "Training assignment details have been downloaded as a PDF.",
                        });
                      } catch (error) {
                        console.error("Error generating PDF:", error);
                        toast({
                          title: "Error",
                          description:
                            "There was an error generating the PDF. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }
                  }}
                >
                  <FileDown className="h-4 w-4 mr-2" />
                  Download PDF
                </Button>
              </div>
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="secondary"
                  className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
                >
                  Close
                </Button>
              </DialogClose>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit Assignment Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[700px] border-muted/40 shadow-lg">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Edit Training Assignment
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              Update the details of this training assignment.
            </DialogDescription>
          </DialogHeader>

          {assignmentToEdit && (
            <div className="py-4">
              <div className="grid gap-4">
                <div>
                  <Label htmlFor="edit-status">Status</Label>
                  <Select
                    value={assignmentToEdit.status}
                    onValueChange={(value) => {
                      setAssignmentToEdit({
                        ...assignmentToEdit,
                        status: value,
                      });
                    }}
                  >
                    <SelectTrigger id="edit-status">
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      {statuses.map((status) => (
                        <SelectItem key={status} value={status}>
                          {status}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="edit-notes">Notes</Label>
                  <textarea
                    id="edit-notes"
                    className="flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    value={assignmentToEdit.notes || ""}
                    onChange={(e) =>
                      setAssignmentToEdit({
                        ...assignmentToEdit,
                        notes: e.target.value,
                      })
                    }
                    placeholder="Add notes about this assignment..."
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="border-t border-muted/20 pt-4">
            <div className="flex gap-2 w-full justify-end">
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="outline"
                  className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
                >
                  Cancel
                </Button>
              </DialogClose>
              <Button
                type="button"
                className="transition-all duration-200 hover:bg-primary/90 shadow-sm"
                onClick={() => {
                  if (assignmentToEdit) {
                    // Update the assignment in the array
                    setTrainingAssignments(
                      trainingAssignments.map((a) =>
                        a.id === assignmentToEdit.id ? assignmentToEdit : a
                      )
                    );

                    const employeeText = assignmentToEdit.employeeIds
                      ? assignmentToEdit.employeeIds.length > 1
                        ? `${assignmentToEdit.employeeIds.length} employees`
                        : "1 employee"
                      : assignmentToEdit.employeeName || "the employee";

                    toast({
                      title: "Assignment Updated",
                      description: `Training assignment for ${employeeText} has been updated.`,
                    });

                    setEditDialogOpen(false);
                  }
                }}
              >
                Save Changes
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* User Details Dialog */}
      <Dialog open={userDetailsDialogOpen} onOpenChange={setUserDetailsDialogOpen}>
        <DialogContent className="sm:max-w-[900px] max-h-[90vh] border-muted/40 shadow-lg flex flex-col">
          <DialogHeader className="bg-muted/10 pb-2">
            <DialogTitle className="text-xl font-semibold">
              Employee Details
            </DialogTitle>
            <DialogDescription className="text-muted-foreground">
              {selectedUserDetails && (
                <>
                  Showing {selectedUserDetails.count} employee{selectedUserDetails.count !== 1 ? 's' : ''} for {selectedUserDetails.organizationInfo.businessUnit} - {selectedUserDetails.organizationInfo.departmentGroup}
                </>
              )}
            </DialogDescription>
          </DialogHeader>

          {selectedUserDetails && (
            <div className="py-4 overflow-y-auto pr-2 flex-1">
              {/* Organization Info Summary */}
              <div className="mb-4 p-4 bg-muted/30 rounded-md border border-muted/40">
                <h3 className="text-sm font-semibold mb-2 text-primary/90">Organization Details</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-3 text-xs">
                  <div>
                    <span className="text-muted-foreground block">Business Unit</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.businessUnit}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Department Group</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.departmentGroup}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Department</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.department}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Division</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.division}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Sub-Division</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.subDivision}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Category</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.category}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Grade</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.grade}</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground block">Designation</span>
                    <span className="font-medium">{selectedUserDetails.organizationInfo.designation}</span>
                  </div>
                </div>
              </div>

              {/* Employee List */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <h3 className="text-sm font-semibold text-primary/90">
                    Employee List ({selectedUserDetails.count} employee{selectedUserDetails.count !== 1 ? 's' : ''})
                  </h3>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={downloadUserDetails}
                    className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                  >
                    <Download size={16} />
                    Download Excel
                  </Button>
                </div>

                <div className="border rounded-md">
                  <Table>
                    <TableHeader className="bg-slate-50">
                      <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                        <TableHead className="font-medium text-left">Employee No</TableHead>
                        <TableHead className="font-medium text-left">Name</TableHead>
                        <TableHead className="font-medium text-left">Email</TableHead>
                        <TableHead className="font-medium text-left">Department</TableHead>
                        <TableHead className="font-medium text-left">Division</TableHead>
                        <TableHead className="font-medium text-center">Status</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {selectedUserDetails.users.map((user, index) => (
                        <TableRow key={user.id} className="hover:bg-muted/30">
                          <TableCell className="font-medium">{user.employeeNo}</TableCell>
                          <TableCell>{user.name}</TableCell>
                          <TableCell className="text-sm text-muted-foreground">{user.email}</TableCell>
                          <TableCell>{user.department}</TableCell>
                          <TableCell>{user.division}</TableCell>
                          <TableCell className="text-center">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              user.status === 'Completed' ? 'bg-green-100 text-green-800' :
                              user.status === 'In Progress' ? 'bg-blue-100 text-blue-800' :
                              user.status === 'Active' ? 'bg-purple-100 text-purple-800' :
                              'bg-gray-100 text-gray-800'
                            }`}>
                              {user.status}
                            </span>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </div>
          )}

          <DialogFooter className="border-t border-muted/20 pt-4">
            <div className="flex gap-2 w-full justify-between">
              <Button
                variant="outline"
                onClick={downloadUserDetails}
                className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
              >
                <Download size={16} />
                Download Excel
              </Button>
              <DialogClose asChild>
                <Button
                  type="button"
                  variant="secondary"
                  className="transition-all duration-200 hover:bg-muted/80 hover:border-primary/30 shadow-sm"
                >
                  Close
                </Button>
              </DialogClose>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Learning Assignment Dialog */}
      <Dialog open={learningAssignmentDialogOpen} onOpenChange={setLearningAssignmentDialogOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              Learning Assignment Details
            </DialogTitle>
            <DialogDescription>
              View and manage training assignment details with area and topic filtering
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto p-6">
            <Card className="border-muted/40 shadow-sm">
              <div className="p-4 border-b border-muted/20">
                <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                    {/* Assigned Area Dropdown */}
                    <div className="flex items-center gap-2">
                      <Label htmlFor="assigned-area" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                        Assigned Area:
                      </Label>
                      <Select
                        value={selectedAssignedArea || ""}
                        onValueChange={(value) => {
                          setSelectedAssignedArea(value);
                          if (!value) {
                            setSelectedAssignedTopic(""); // Reset topic when clearing area
                          }
                        }}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Select Area" />
                        </SelectTrigger>
                        <SelectContent>
                          {trainingAreas.map((area) => (
                            <SelectItem key={area} value={area}>
                              {area}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Assigned Topic Dropdown */}
                    <div className="flex items-center gap-2">
                      <Label htmlFor="assigned-topic" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                        Assigned Topic:
                      </Label>
                      <Select
                        value={selectedAssignedTopic || ""}
                        onValueChange={(value) => setSelectedAssignedTopic(value)}
                        disabled={!selectedAssignedArea}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Select Topic" />
                        </SelectTrigger>
                        <SelectContent>
                          {(() => {
                            // If area is selected, show only topics for that area
                            if (selectedAssignedArea && selectedAssignedArea !== "") {
                              const areaTopics = trainingTopics[selectedAssignedArea as keyof typeof trainingTopics];
                              if (Array.isArray(areaTopics)) {
                                return areaTopics.map((topic: string) => (
                                  <SelectItem key={topic} value={topic}>
                                    {topic}
                                  </SelectItem>
                                ));
                              }
                              return [];
                            }

                            // If no area is selected, show all topics
                            const allTopics = Object.values(trainingTopics).flat();
                            const uniqueTopics = [...new Set(allTopics)];
                            return uniqueTopics.map((topic: string) => (
                              <SelectItem key={topic} value={topic}>
                                {topic}
                              </SelectItem>
                            ));
                          })()}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                    onClick={() => {
                      // Export filtered learning assignment details to Excel
                      try {
                        // Filter assignments based on selected area and topic
                        let filteredAssignments = trainingAssignments.filter(assignment =>
                          assignment.trainingProgram.includes("→") // Only training assignments
                        );

                        if (selectedAssignedArea || selectedAssignedTopic) {
                          filteredAssignments = filteredAssignments.filter(assignment => {
                            // Extract areas and topics from the training program
                            const programParts = assignment.trainingProgram
                              .split(" | ")
                              .map(program => program.split(" → "));

                            return programParts.some(parts => {
                              const [area, topic] = parts;
                              const areaMatch = !selectedAssignedArea || area === selectedAssignedArea;
                              const topicMatch = !selectedAssignedTopic || topic === selectedAssignedTopic;
                              return areaMatch && topicMatch;
                            });
                          });
                        }

                        // Create export data
                        const exportData = filteredAssignments.flatMap(assignment => {
                          const assignedEmployees = assignment.employeeIds?.map((empId) => {
                            return `Employee ${empId}`;
                          }) || [assignment.employeeName || "Unknown Employee"];

                          return assignedEmployees.map(employeeName => ({
                            "Business Unit": assignment.businessUnit,
                            "Department Group": assignment.departmentGroup,
                            "Department": assignment.department,
                            "Division": assignment.division,
                            "Sub-Division": assignment.subDivision,
                            "Category": assignment.category,
                            "Grade": assignment.grade,
                            "Designation": assignment.designation,
                            "Assigned Employee": employeeName,
                            "Status": assignment.status,
                            "Training Program": assignment.trainingProgram
                          }));
                        });

                        // Export to Excel
                        const ws = XLSX.utils.json_to_sheet(exportData);
                        const wb = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(wb, ws, "Learning Assignment Details");
                        XLSX.writeFile(wb, `Learning_Assignment_Details_${new Date().toISOString().split('T')[0]}.xlsx`);

                        toast({
                          title: "Excel Generated",
                          description: "Learning assignment details have been exported to Excel.",
                        });
                      } catch (error) {
                        console.error("Error exporting to Excel:", error);
                        toast({
                          title: "Error",
                          description: "There was an error exporting to Excel. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <FileDown size={16} />
                    Export to Excel
                  </Button>
                </div>
              </div>

              {/* Assignment Details Table */}
              <div className="px-4 py-6">
                {(() => {
                  // Filter assignments based on selected area and topic
                  let filteredAssignments = trainingAssignments.filter(assignment =>
                    assignment.trainingProgram.includes("→") // Only training assignments
                  );

                  if (selectedAssignedArea || selectedAssignedTopic) {
                    filteredAssignments = filteredAssignments.filter(assignment => {
                      // Extract areas and topics from the training program
                      const programParts = assignment.trainingProgram
                        .split(" | ")
                        .map(program => program.split(" → "));

                      return programParts.some(parts => {
                        const [area, topic] = parts;
                        const areaMatch = !selectedAssignedArea || area === selectedAssignedArea;
                        const topicMatch = !selectedAssignedTopic || topic === selectedAssignedTopic;
                        return areaMatch && topicMatch;
                      });
                    });
                  }

                  if (filteredAssignments.length === 0) {
                    return (
                      <div className="text-center py-8">
                        <div className="text-muted-foreground">
                          {selectedAssignedArea || selectedAssignedTopic
                            ? "No assignments found for the selected area and topic."
                            : "Please select an area and topic to view assignment details."
                          }
                        </div>
                      </div>
                    );
                  }

                  // Generate table rows from filtered assignments
                  const generateTableRows = () => {
                    const rows: any[] = [];

                    filteredAssignments.forEach(assignment => {
                      // Parse business units, departments, etc.
                      const businessUnits = assignment.businessUnit.split(", ");
                      const departmentGroups = assignment.departmentGroup.split(", ");
                      const departments = assignment.department.split(", ");
                      const divisions = assignment.division.split(", ");
                      const subDivisions = assignment.subDivision.split(", ");
                      const categories = assignment.category.split(", ");
                      const grades = assignment.grade.split(", ");
                      const designations = assignment.designation.split(", ");

                      // Generate rows for all combinations
                      businessUnits.forEach((businessUnit) => {
                        departmentGroups.forEach((departmentGroup) => {
                          departments.forEach((department) => {
                            divisions.forEach((division) => {
                              subDivisions.forEach((subDivision) => {
                                categories.forEach((category) => {
                                  grades.forEach((grade) => {
                                    designations.forEach((designation) => {
                                      // Generate static random employee count
                                      const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                                      let hash = 0;
                                      for (let i = 0; i < seed.length; i++) {
                                        const char = seed.charCodeAt(i);
                                        hash = ((hash << 5) - hash) + char;
                                        hash = hash & hash;
                                      }
                                      const employeeCount = Math.abs(hash % 15) + 1;

                                      const row = {
                                        businessUnit: businessUnit.trim(),
                                        departmentGroup: departmentGroup.trim(),
                                        department: department.trim(),
                                        division: division.trim(),
                                        subDivision: subDivision.trim(),
                                        category: category.trim(),
                                        grade: grade.trim(),
                                        designation: designation.trim(),
                                        employeeCount: employeeCount,
                                        assignedEmployee: `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`,
                                        status: assignment.status,
                                        dueDate: assignment.dueDate || "N/A",
                                      };
                                      rows.push(row);
                                    });
                                  });
                                });
                              });
                            });
                          });
                        });
                      });
                    });

                    return rows;
                  };

                  const tableRows = generateTableRows();

                  return (
                    <div className="space-y-4">
                      {/* Assignment Details Table */}
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader className="bg-slate-50">
                            <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                              <TableHead className="font-medium text-left">Business Unit</TableHead>
                              <TableHead className="font-medium text-left">Department Group</TableHead>
                              <TableHead className="font-medium text-left">Department</TableHead>
                              <TableHead className="font-medium text-left">Division</TableHead>
                              <TableHead className="font-medium text-left">Sub-Division</TableHead>
                              <TableHead className="font-medium text-left">Category</TableHead>
                              <TableHead className="font-medium text-left">Grade</TableHead>
                              <TableHead className="font-medium text-left">Designation</TableHead>
                              <TableHead className="font-medium text-left">Assigned Employee</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {tableRows.map((row, index) => (
                              <TableRow key={index} className="hover:bg-muted/50">
                                <TableCell className="font-medium">{row.businessUnit}</TableCell>
                                <TableCell>{row.departmentGroup}</TableCell>
                                <TableCell>{row.department}</TableCell>
                                <TableCell>{row.division}</TableCell>
                                <TableCell>{row.subDivision}</TableCell>
                                <TableCell>{row.category}</TableCell>
                                <TableCell>{row.grade}</TableCell>
                                <TableCell>{row.designation}</TableCell>
                                <TableCell>
                                  <Button
                                    variant="link"
                                    className="p-0 h-auto text-blue-600 hover:text-blue-800"
                                    onClick={() => {
                                      // Handle employee count click - show user details popup
                                      toast({
                                        title: "Employee Details",
                                        description: `Showing details for ${row.assignedEmployee} in ${row.businessUnit}`,
                                      });
                                    }}
                                  >
                                    {row.assignedEmployee}
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {/* CFD Assignment Dialog */}
      <Dialog open={cfdAssignmentDialogOpen} onOpenChange={setCfdAssignmentDialogOpen}>
        <DialogContent className="max-w-7xl max-h-[90vh] overflow-hidden flex flex-col">
          <DialogHeader>
            <DialogTitle className="text-xl font-semibold">
              CFD Assignment Details
            </DialogTitle>
            <DialogDescription>
              View and manage CFD assignment details with area, topic, and unit filtering
            </DialogDescription>
          </DialogHeader>

          <div className="flex-1 overflow-auto p-6">
            <Card className="border-muted/40 shadow-sm">
              <div className="p-4 border-b border-muted/20">
                <div className="flex flex-col md:flex-row gap-4 items-start md:items-center justify-between">
                  <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
                    {/* CFD Area Dropdown */}
                    <div className="flex items-center gap-2">
                      <Label htmlFor="cfd-area" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                        CFD Area:
                      </Label>
                      <Select
                        value={selectedCfdArea || ""}
                        onValueChange={(value) => {
                          setSelectedCfdArea(value);
                          if (!value) {
                            setSelectedCfdTopic("");
                            setSelectedCfdUnit("");
                          }
                        }}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Select Area" />
                        </SelectTrigger>
                        <SelectContent>
                          {cfdAreas.map((area) => (
                            <SelectItem key={area} value={area}>
                              {area}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* CFD Topic Dropdown */}
                    <div className="flex items-center gap-2">
                      <Label htmlFor="cfd-topic" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                        CFD Topic:
                      </Label>
                      <Select
                        value={selectedCfdTopic || ""}
                        onValueChange={(value) => {
                          setSelectedCfdTopic(value);
                          if (!value) {
                            setSelectedCfdUnit("");
                          }
                        }}
                        disabled={!selectedCfdArea}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Select Topic" />
                        </SelectTrigger>
                        <SelectContent>
                          {(() => {
                            if (selectedCfdArea && selectedCfdArea !== "") {
                              const areaTopics = cfdTopics[selectedCfdArea as keyof typeof cfdTopics];
                              if (Array.isArray(areaTopics)) {
                                return areaTopics.map((topic: string) => (
                                  <SelectItem key={topic} value={topic}>
                                    {topic}
                                  </SelectItem>
                                ));
                              }
                              return [];
                            }

                            const allTopics = Object.values(cfdTopics).flat();
                            const uniqueTopics = [...new Set(allTopics)];
                            return uniqueTopics.map((topic: string) => (
                              <SelectItem key={topic} value={topic}>
                                {topic}
                              </SelectItem>
                            ));
                          })()}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* CFD Unit Dropdown */}
                    <div className="flex items-center gap-2">
                      <Label htmlFor="cfd-unit" className="text-sm font-medium text-slate-700 whitespace-nowrap">
                        CFD Unit:
                      </Label>
                      <Select
                        value={selectedCfdUnit || ""}
                        onValueChange={(value) => setSelectedCfdUnit(value)}
                        disabled={!selectedCfdTopic}
                      >
                        <SelectTrigger className="w-48">
                          <SelectValue placeholder="Select Unit" />
                        </SelectTrigger>
                        <SelectContent>
                          {(() => {
                            if (selectedCfdTopic && selectedCfdTopic !== "") {
                              const topicUnits = cfdUnits[selectedCfdTopic as keyof typeof cfdUnits];
                              if (Array.isArray(topicUnits)) {
                                return topicUnits.map((unit: string) => (
                                  <SelectItem key={unit} value={unit}>
                                    {unit}
                                  </SelectItem>
                                ));
                              }
                              return [];
                            }
                            return [];
                          })()}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <Button
                    variant="outline"
                    className="gap-2 transition-all duration-200 hover:bg-muted/80 hover:border-primary/30"
                    onClick={() => {
                      // Export filtered CFD assignment details to Excel
                      try {
                        // Filter assignments based on selected area, topic, and unit
                        let filteredAssignments = trainingAssignments.filter(assignment =>
                          !assignment.trainingProgram.includes("→") || assignment.trainingProgram.includes("Checklist") // CFD assignments
                        );

                        if (selectedCfdArea || selectedCfdTopic || selectedCfdUnit) {
                          filteredAssignments = filteredAssignments.filter(assignment => {
                            // Extract areas, topics, and units from the training program
                            const programParts = assignment.trainingProgram
                              .split(" | ")
                              .map(program => program.split(" → "));

                            return programParts.some(parts => {
                              const [area, topic, unit] = parts;
                              const areaMatch = !selectedCfdArea || area === selectedCfdArea;
                              const topicMatch = !selectedCfdTopic || topic === selectedCfdTopic;
                              const unitMatch = !selectedCfdUnit || unit === selectedCfdUnit;
                              return areaMatch && topicMatch && unitMatch;
                            });
                          });
                        }

                        // Create export data
                        const exportData = filteredAssignments.flatMap(assignment => {
                          const assignedEmployees = assignment.employeeIds?.map((empId) => {
                            return `Employee ${empId}`;
                          }) || [assignment.employeeName || "Unknown Employee"];

                          return assignedEmployees.map(employeeName => ({
                            "Business Unit": assignment.businessUnit,
                            "Department Group": assignment.departmentGroup,
                            "Department": assignment.department,
                            "Division": assignment.division,
                            "Sub-Division": assignment.subDivision,
                            "Category": assignment.category,
                            "Grade": assignment.grade,
                            "Designation": assignment.designation,
                            "Assigned Employee": employeeName,
                            "Status": assignment.status,
                            "Training Program": assignment.trainingProgram
                          }));
                        });

                        // Export to Excel
                        const ws = XLSX.utils.json_to_sheet(exportData);
                        const wb = XLSX.utils.book_new();
                        XLSX.utils.book_append_sheet(wb, ws, "CFD Assignment Details");
                        XLSX.writeFile(wb, `CFD_Assignment_Details_${new Date().toISOString().split('T')[0]}.xlsx`);

                        toast({
                          title: "Excel Generated",
                          description: "CFD assignment details have been exported to Excel.",
                        });
                      } catch (error) {
                        console.error("Error exporting to Excel:", error);
                        toast({
                          title: "Error",
                          description: "There was an error exporting to Excel. Please try again.",
                          variant: "destructive",
                        });
                      }
                    }}
                  >
                    <FileDown size={16} />
                    Export to Excel
                  </Button>
                </div>
              </div>

              {/* CFD Assignment Details Table */}
              <div className="px-4 py-6">
                {(() => {
                  // Filter CFD assignments based on selected area, topic, and unit
                  let filteredCfdAssignments = trainingAssignments.filter(assignment =>
                    !assignment.trainingProgram.includes("→") || assignment.trainingProgram.includes("Checklist") // CFD assignments
                  );

                  if (selectedCfdArea || selectedCfdTopic || selectedCfdUnit) {
                    filteredCfdAssignments = filteredCfdAssignments.filter(assignment => {
                      // Extract areas, topics, and units from the training program
                      const programParts = assignment.trainingProgram
                        .split(" | ")
                        .map(program => program.split(" → "));

                      return programParts.some(parts => {
                        const [area, topic, unit] = parts;
                        const areaMatch = !selectedCfdArea || area === selectedCfdArea;
                        const topicMatch = !selectedCfdTopic || topic === selectedCfdTopic;
                        const unitMatch = !selectedCfdUnit || unit === selectedCfdUnit;
                        return areaMatch && topicMatch && unitMatch;
                      });
                    });
                  }

                  if (filteredCfdAssignments.length === 0) {
                    return (
                      <div className="text-center py-8">
                        <div className="text-muted-foreground">
                          {selectedCfdArea || selectedCfdTopic || selectedCfdUnit
                            ? "No CFD assignments found for the selected criteria."
                            : "Please select an area, topic, and unit to view CFD assignment details."
                          }
                        </div>
                      </div>
                    );
                  }

                  // Generate table rows from filtered CFD assignments
                  const generateCfdTableRows = () => {
                    const rows: any[] = [];

                    filteredCfdAssignments.forEach(assignment => {
                      // Parse business units, departments, etc.
                      const businessUnits = assignment.businessUnit.split(", ");
                      const departmentGroups = assignment.departmentGroup.split(", ");
                      const departments = assignment.department.split(", ");
                      const divisions = assignment.division.split(", ");
                      const subDivisions = assignment.subDivision.split(", ");
                      const categories = assignment.category.split(", ");
                      const grades = assignment.grade.split(", ");
                      const designations = assignment.designation.split(", ");

                      // Generate rows for all combinations
                      businessUnits.forEach((businessUnit) => {
                        departmentGroups.forEach((departmentGroup) => {
                          departments.forEach((department) => {
                            divisions.forEach((division) => {
                              subDivisions.forEach((subDivision) => {
                                categories.forEach((category) => {
                                  grades.forEach((grade) => {
                                    designations.forEach((designation) => {
                                      // Generate static random employee count
                                      const seed = `${businessUnit}-${departmentGroup}-${department}-${division}-${subDivision}-${category}-${grade}-${designation}`;
                                      let hash = 0;
                                      for (let i = 0; i < seed.length; i++) {
                                        const char = seed.charCodeAt(i);
                                        hash = ((hash << 5) - hash) + char;
                                        hash = hash & hash;
                                      }
                                      const employeeCount = Math.abs(hash % 15) + 1;

                                      const row = {
                                        businessUnit: businessUnit.trim(),
                                        departmentGroup: departmentGroup.trim(),
                                        department: department.trim(),
                                        division: division.trim(),
                                        subDivision: subDivision.trim(),
                                        category: category.trim(),
                                        grade: grade.trim(),
                                        designation: designation.trim(),
                                        employeeCount: employeeCount,
                                        assignedEmployee: `${employeeCount} Employee${employeeCount !== 1 ? 's' : ''}`,
                                        status: assignment.status,
                                        dueDate: assignment.dueDate || "N/A",
                                      };
                                      rows.push(row);
                                    });
                                  });
                                });
                              });
                            });
                          });
                        });
                      });
                    });

                    return rows;
                  };

                  const cfdTableRows = generateCfdTableRows();

                  return (
                    <div className="space-y-4">
                      {/* CFD Assignment Details Table */}
                      <div className="overflow-x-auto">
                        <Table>
                          <TableHeader className="bg-slate-50">
                            <TableRow className="text-xs text-slate-600 uppercase tracking-wider">
                              <TableHead className="font-medium text-left">Business Unit</TableHead>
                              <TableHead className="font-medium text-left">Department Group</TableHead>
                              <TableHead className="font-medium text-left">Department</TableHead>
                              <TableHead className="font-medium text-left">Division</TableHead>
                              <TableHead className="font-medium text-left">Sub-Division</TableHead>
                              <TableHead className="font-medium text-left">Category</TableHead>
                              <TableHead className="font-medium text-left">Grade</TableHead>
                              <TableHead className="font-medium text-left">Designation</TableHead>
                              <TableHead className="font-medium text-left">Assigned Employee</TableHead>
                            </TableRow>
                          </TableHeader>
                          <TableBody>
                            {cfdTableRows.map((row, index) => (
                              <TableRow key={index} className="hover:bg-muted/50">
                                <TableCell className="font-medium">{row.businessUnit}</TableCell>
                                <TableCell>{row.departmentGroup}</TableCell>
                                <TableCell>{row.department}</TableCell>
                                <TableCell>{row.division}</TableCell>
                                <TableCell>{row.subDivision}</TableCell>
                                <TableCell>{row.category}</TableCell>
                                <TableCell>{row.grade}</TableCell>
                                <TableCell>{row.designation}</TableCell>
                                <TableCell>
                                  <Button
                                    variant="link"
                                    className="p-0 h-auto text-purple-600 hover:text-purple-800"
                                    onClick={() => {
                                      // Handle employee count click - show user details popup
                                      toast({
                                        title: "Employee Details",
                                        description: `Showing details for ${row.assignedEmployee} in ${row.businessUnit}`,
                                      });
                                    }}
                                  >
                                    {row.assignedEmployee}
                                  </Button>
                                </TableCell>
                              </TableRow>
                            ))}
                          </TableBody>
                        </Table>
                      </div>
                    </div>
                  );
                })()}
              </div>
            </Card>
          </div>
        </DialogContent>
      </Dialog>

      {/* Training Library Dialogs */}
      <ViewAssignmentDialog
        open={trainingViewAssignmentOpen}
        onOpenChange={setTrainingViewAssignmentOpen}
        assignmentItem={trainingSelectedAssignmentItem}
        assignedUsers={trainingSelectedAssignmentItem ? trainingAssignedUsers[trainingSelectedAssignmentItem.topicId] || [] : []}
      />

      <UserSelectionDialog
        open={trainingUserSelectionOpen}
        onOpenChange={setTrainingUserSelectionOpen}
        onAssign={handleTrainingUserAssignment}
        title="Assign Users to Training"
        assignmentItem={trainingSelectedItemForAssignment}
      />

      {/* Document Library Dialogs */}
      <ViewAssignmentDialog
        open={docViewAssignmentOpen}
        onOpenChange={setDocViewAssignmentOpen}
        assignmentItem={docSelectedAssignmentItem}
        assignedUsers={docSelectedAssignmentItem ? docAssignedUsers[docSelectedAssignmentItem.topicId] || [] : []}
      />

      <UserSelectionDialog
        open={docUserSelectionOpen}
        onOpenChange={setDocUserSelectionOpen}
        onAssign={handleDocUserAssignment}
        title="Assign Users to Document"
        assignmentItem={docSelectedItemForAssignment}
      />

      {/* Forms Library Dialogs */}
      <ViewAssignmentDialog
        open={formsViewAssignmentOpen}
        onOpenChange={setFormsViewAssignmentOpen}
        assignmentItem={formsSelectedAssignmentItem}
        assignedUsers={formsSelectedAssignmentItem ? formsAssignedUsers[formsSelectedAssignmentItem.topicId] || [] : []}
      />

      <UserSelectionDialog
        open={formsUserSelectionOpen}
        onOpenChange={setFormsUserSelectionOpen}
        onAssign={handleFormsUserAssignment}
        title="Assign Users to Form"
        assignmentItem={formsSelectedItemForAssignment}
      />

      {/* Checklist Library Dialogs */}
      <ViewAssignmentDialog
        open={checklistViewAssignmentOpen}
        onOpenChange={setChecklistViewAssignmentOpen}
        assignmentItem={checklistSelectedAssignmentItem}
        assignedUsers={checklistSelectedAssignmentItem ? checklistAssignedUsers[checklistSelectedAssignmentItem.topicId] || [] : []}
      />

      <UserSelectionDialog
        open={checklistUserSelectionOpen}
        onOpenChange={setChecklistUserSelectionOpen}
        onAssign={handleChecklistUserAssignment}
        title="Assign Users to Checklist"
        assignmentItem={checklistSelectedItemForAssignment}
      />
          </Card>
        </motion.div>
      </motion.div>
    </>
  );
}
